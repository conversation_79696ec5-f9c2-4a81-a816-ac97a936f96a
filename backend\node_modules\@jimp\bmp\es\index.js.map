{"version": 3, "file": "index.js", "names": ["BMP", "scan", "MIME_TYPE", "MIME_TYPE_SECOND", "toAGBR", "image", "bitmap", "width", "height", "x", "y", "index", "red", "data", "green", "blue", "alpha", "fromAGBR", "is_with_alpha", "decode", "encode", "mime", "constants", "MIME_BMP", "MIME_X_MS_BMP", "decoders", "encoders"], "sources": ["../src/index.js"], "sourcesContent": ["import BMP from \"bmp-js\";\nimport { scan } from \"@jimp/utils\";\n\nconst MIME_TYPE = \"image/bmp\";\nconst MIME_TYPE_SECOND = \"image/x-ms-bmp\";\n\nfunction toAGBR(image) {\n  return scan(\n    image,\n    0,\n    0,\n    image.bitmap.width,\n    image.bitmap.height,\n    function (x, y, index) {\n      const red = this.bitmap.data[index + 0];\n      const green = this.bitmap.data[index + 1];\n      const blue = this.bitmap.data[index + 2];\n      const alpha = this.bitmap.data[index + 3];\n\n      this.bitmap.data[index + 0] = alpha;\n      this.bitmap.data[index + 1] = blue;\n      this.bitmap.data[index + 2] = green;\n      this.bitmap.data[index + 3] = red;\n    }\n  ).bitmap;\n}\n\nfunction fromAGBR(bitmap) {\n  return scan(\n    { bitmap },\n    0,\n    0,\n    bitmap.width,\n    bitmap.height,\n    function (x, y, index) {\n      const alpha = this.bitmap.data[index + 0];\n      const blue = this.bitmap.data[index + 1];\n      const green = this.bitmap.data[index + 2];\n      const red = this.bitmap.data[index + 3];\n\n      this.bitmap.data[index + 0] = red;\n      this.bitmap.data[index + 1] = green;\n      this.bitmap.data[index + 2] = blue;\n      this.bitmap.data[index + 3] = bitmap.is_with_alpha ? alpha : 0xff;\n    }\n  ).bitmap;\n}\n\nconst decode = (data) => fromAGBR(BMP.decode(data));\nconst encode = (image) => BMP.encode(toAGBR(image)).data;\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"bmp\"] },\n\n  constants: {\n    MIME_BMP: MIME_TYPE,\n    MIME_X_MS_BMP: MIME_TYPE_SECOND,\n  },\n\n  decoders: {\n    [MIME_TYPE]: decode,\n    [MIME_TYPE_SECOND]: decode,\n  },\n\n  encoders: {\n    [MIME_TYPE]: encode,\n    [MIME_TYPE_SECOND]: encode,\n  },\n});\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,QAAQ;AACxB,SAASC,IAAI,QAAQ,aAAa;AAElC,MAAMC,SAAS,GAAG,WAAW;AAC7B,MAAMC,gBAAgB,GAAG,gBAAgB;AAEzC,SAASC,MAAM,CAACC,KAAK,EAAE;EACrB,OAAOJ,IAAI,CACTI,KAAK,EACL,CAAC,EACD,CAAC,EACDA,KAAK,CAACC,MAAM,CAACC,KAAK,EAClBF,KAAK,CAACC,MAAM,CAACE,MAAM,EACnB,UAAUC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAE;IACrB,MAAMC,GAAG,GAAG,IAAI,CAACN,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IACvC,MAAMG,KAAK,GAAG,IAAI,CAACR,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IACzC,MAAMI,IAAI,GAAG,IAAI,CAACT,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IACxC,MAAMK,KAAK,GAAG,IAAI,CAACV,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IAEzC,IAAI,CAACL,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGK,KAAK;IACnC,IAAI,CAACV,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGI,IAAI;IAClC,IAAI,CAACT,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGG,KAAK;IACnC,IAAI,CAACR,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGC,GAAG;EACnC,CAAC,CACF,CAACN,MAAM;AACV;AAEA,SAASW,QAAQ,CAACX,MAAM,EAAE;EACxB,OAAOL,IAAI,CACT;IAAEK;EAAO,CAAC,EACV,CAAC,EACD,CAAC,EACDA,MAAM,CAACC,KAAK,EACZD,MAAM,CAACE,MAAM,EACb,UAAUC,CAAC,EAAEC,CAAC,EAAEC,KAAK,EAAE;IACrB,MAAMK,KAAK,GAAG,IAAI,CAACV,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IACzC,MAAMI,IAAI,GAAG,IAAI,CAACT,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IACxC,MAAMG,KAAK,GAAG,IAAI,CAACR,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IACzC,MAAMC,GAAG,GAAG,IAAI,CAACN,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;IAEvC,IAAI,CAACL,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGC,GAAG;IACjC,IAAI,CAACN,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGG,KAAK;IACnC,IAAI,CAACR,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGI,IAAI;IAClC,IAAI,CAACT,MAAM,CAACO,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGL,MAAM,CAACY,aAAa,GAAGF,KAAK,GAAG,IAAI;EACnE,CAAC,CACF,CAACV,MAAM;AACV;AAEA,MAAMa,MAAM,GAAIN,IAAI,IAAKI,QAAQ,CAACjB,GAAG,CAACmB,MAAM,CAACN,IAAI,CAAC,CAAC;AACnD,MAAMO,MAAM,GAAIf,KAAK,IAAKL,GAAG,CAACoB,MAAM,CAAChB,MAAM,CAACC,KAAK,CAAC,CAAC,CAACQ,IAAI;AAExD,gBAAe,OAAO;EACpBQ,IAAI,EAAE;IAAE,CAACnB,SAAS,GAAG,CAAC,KAAK;EAAE,CAAC;EAE9BoB,SAAS,EAAE;IACTC,QAAQ,EAAErB,SAAS;IACnBsB,aAAa,EAAErB;EACjB,CAAC;EAEDsB,QAAQ,EAAE;IACR,CAACvB,SAAS,GAAGiB,MAAM;IACnB,CAAChB,gBAAgB,GAAGgB;EACtB,CAAC;EAEDO,QAAQ,EAAE;IACR,CAACxB,SAAS,GAAGkB,MAAM;IACnB,CAACjB,gBAAgB,GAAGiB;EACtB;AACF,CAAC,CAAC"}