{"version": 3, "file": "promisify.js", "names": ["promisify", "fun", "ctx", "args", "Promise", "resolve", "reject", "push", "err", "data", "bind"], "sources": ["../../src/utils/promisify.js"], "sourcesContent": ["const promisify = (fun, ctx, ...args) =>\n  new Promise((resolve, reject) => {\n    args.push((err, data) => {\n      if (err) {\n        reject(err);\n      }\n\n      resolve(data);\n    });\n    fun.bind(ctx)(...args);\n  });\n\nexport default promisify;\n"], "mappings": ";;;;;;AAAA,MAAMA,SAAS,GAAG,UAACC,GAAG,EAAEC,GAAG;EAAA,kCAAKC,IAAI;IAAJA,IAAI;EAAA;EAAA,OAClC,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IAC/BH,IAAI,CAACI,IAAI,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;MACvB,IAAID,GAAG,EAAE;QACPF,MAAM,CAACE,GAAG,CAAC;MACb;MAEAH,OAAO,CAACI,IAAI,CAAC;IACf,CAAC,CAAC;IACFR,GAAG,CAACS,IAAI,CAACR,GAAG,CAAC,CAAC,GAAGC,IAAI,CAAC;EACxB,CAAC,CAAC;AAAA;AAAC,eAEUH,SAAS;AAAA;AAAA;AAAA"}