import React, { useState, useEffect } from 'react';
import { ChevronRightIcon, FunnelIcon, ArrowRightOnRectangleIcon, UserIcon } from '@heroicons/react/24/outline';
import VariantsTab from './VariantsTab';
import { useToast } from '../hooks/useToast';

const UploadScreen = () => {
  const [activeTab, setActiveTab] = useState('drafts');
  const [uploadCounts, setUploadCounts] = useState({
    drafts: 1,
    scheduled: 0,
    recurring: 0
  });
  const [isLoading, setIsLoading] = useState(false);
  const { showToast } = useToast();

  // Mock user data
  const user = {
    firstName: 'Demo',
    lastName: 'User',
    accountType: 'pro'
  };

  // Fetch upload counts on component mount
  useEffect(() => {
    fetchUploadCounts();
  }, []);

  const fetchUploadCounts = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/v1/uploads/counts');
      if (response.ok) {
        const data = await response.json();
        setUploadCounts(data.counts || uploadCounts);
      }
    } catch (error) {
      console.error('Failed to fetch upload counts:', error);
      showToast('Failed to load upload counts', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const getTotalUploads = () => {
    return uploadCounts.drafts + uploadCounts.scheduled + uploadCounts.recurring;
  };

  const handleTabChange = (tabName) => {
    setActiveTab(tabName);
  };

  const handleFilterClick = () => {
    // TODO: Implement filter modal
    showToast('Filter functionality coming soon', 'info');
  };

  const handleLogout = () => {
    showToast('تم تسجيل الخروج بنجاح', 'success');
    // In a real app, this would clear auth state
  };

  const tabs = [
    { id: 'drafts', label: 'المسودات', count: uploadCounts.drafts },
    { id: 'scheduled', label: 'المجدولة', count: uploadCounts.scheduled },
    { id: 'recurring', label: 'المتكررة', count: uploadCounts.recurring }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb Navigation */}
      <nav className="pt-4 px-6" aria-label="Breadcrumb">
        <ol className="flex items-center space-x-2 text-sm text-gray-500" dir="rtl">
          <li>
            <a href="/dashboard" className="hover:text-gray-700 transition-colors">
              لوحة التحكم
            </a>
          </li>
          <ChevronRightIcon className="h-4 w-4" />
          <li>
            <a href="/products" className="hover:text-gray-700 transition-colors">
              المنتجات
            </a>
          </li>
          <ChevronRightIcon className="h-4 w-4" />
          <li className="text-gray-800 font-medium" aria-current="page">
            استيراد المنتجات
          </li>
        </ol>
      </nav>

      {/* Page Header */}
      <div className="px-6 mt-6" dir="rtl">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <h1 className="text-3xl font-semibold text-gray-900">
              استيراد المنتجات
            </h1>
            <span className="mr-2 text-3xl text-gray-500">
              ({getTotalUploads()})
            </span>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
              <UserIcon className="h-4 w-4" />
              <span>{user?.firstName} {user?.lastName}</span>
              {user?.accountType === 'trial' && (
                <span className="bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-medium">
                  تجريبي
                </span>
              )}
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center space-x-1 space-x-reverse text-sm text-gray-500 hover:text-gray-700 transition-colors"
              title="تسجيل الخروج"
            >
              <ArrowRightOnRectangleIcon className="h-4 w-4" />
              <span>تسجيل الخروج</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="px-6 mt-8" dir="rtl">
        <div className="border-b border-gray-200 bg-white">
          <div className="flex items-center justify-between px-6 py-0">
            <nav className="flex space-x-8 space-x-reverse" aria-label="Tabs">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`
                    py-3 px-4 text-sm font-medium border-b-2 transition-all duration-200
                    ${activeTab === tab.id
                      ? 'border-amber-500 text-gray-800 bg-amber-50'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                    }
                  `}
                  aria-current={activeTab === tab.id ? 'page' : undefined}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </nav>

            {/* Add Filter Button */}
            <button
              onClick={handleFilterClick}
              className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              <FunnelIcon className="h-4 w-4 ml-2 text-gray-500" />
              إضافة فلتر
            </button>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="px-6 mt-0">
        {activeTab === 'drafts' && (
          <VariantsTab 
            onCountUpdate={(newCount) => 
              setUploadCounts(prev => ({ ...prev, drafts: newCount }))
            }
          />
        )}
        {activeTab === 'scheduled' && (
          <div className="py-12 text-center" dir="rtl">
            <div className="text-gray-500">
              <h3 className="text-lg font-medium">لا توجد منتجات مجدولة</h3>
              <p className="mt-2">ستظهر المنتجات المجدولة هنا عند إنشائها.</p>
            </div>
          </div>
        )}
        {activeTab === 'recurring' && (
          <div className="py-12 text-center" dir="rtl">
            <div className="text-gray-500">
              <h3 className="text-lg font-medium">لا توجد منتجات متكررة</h3>
              <p className="mt-2">ستظهر جداول الرفع المتكررة هنا عند تكوينها.</p>
            </div>
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 flex items-center space-x-3 space-x-reverse" dir="rtl">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="text-gray-700">جاري التحميل...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadScreen;
