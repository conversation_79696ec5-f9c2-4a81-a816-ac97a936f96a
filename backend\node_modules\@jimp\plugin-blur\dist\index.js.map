{"version": 3, "file": "index.js", "names": ["blur", "r", "cb", "throwError", "call", "rsum", "gsum", "bsum", "asum", "x", "y", "i", "p", "p1", "p2", "yp", "yi", "yw", "pa", "wm", "bitmap", "width", "hm", "height", "rad1", "mulSum", "mulTable", "shgSum", "shgTable", "red", "green", "blue", "alpha", "vmin", "vmax", "iterations", "data", "isNodePattern"], "sources": ["../src/index.js"], "sourcesContent": ["import { throwError, isNode<PERSON>attern } from \"@jimp/utils\";\nimport { mulTable, shgTable } from \"./blur-tables\";\n\n/*\n    Superfast Blur (0.5)\n    http://www.quasimondo.com/BoxBlurForCanvas/FastBlur.js\n\n    Copyright (c) 2011 <PERSON>\n\n    Permission is hereby granted, free of charge, to any person\n    obtaining a copy of this software and associated documentation\n    files (the \"Software\"), to deal in the Software without\n    restriction, including without limitation the rights to use,\n    copy, modify, merge, publish, distribute, sublicense, and/or sell\n    copies of the Software, and to permit persons to whom the\n    Software is furnished to do so, subject to the following\n    conditions:\n\n    The above copyright notice and this permission notice shall be\n    included in all copies or substantial portions of the Software.\n\n    THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n    EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n    OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n    NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n    HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n    WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n    FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n    OTHER DEALINGS IN THE SOFTWARE.\n*/\n\nexport default () => ({\n  /**\n   * A fast blur algorithm that produces similar effect to a Gaussian blur - but MUCH quicker\n   * @param {number} r the pixel radius of the blur\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  blur(r, cb) {\n    if (typeof r !== \"number\")\n      return throwError.call(this, \"r must be a number\", cb);\n    if (r < 1) return throwError.call(this, \"r must be greater than 0\", cb);\n\n    let rsum;\n    let gsum;\n    let bsum;\n    let asum;\n    let x;\n    let y;\n    let i;\n    let p;\n    let p1;\n    let p2;\n    let yp;\n    let yi;\n    let yw;\n    let pa;\n\n    const wm = this.bitmap.width - 1;\n    const hm = this.bitmap.height - 1;\n    // const wh = this.bitmap.width * this.bitmap.height;\n    const rad1 = r + 1;\n\n    const mulSum = mulTable[r];\n    const shgSum = shgTable[r];\n\n    const red = [];\n    const green = [];\n    const blue = [];\n    const alpha = [];\n\n    const vmin = [];\n    const vmax = [];\n\n    let iterations = 2;\n\n    while (iterations-- > 0) {\n      yi = 0;\n      yw = 0;\n\n      for (y = 0; y < this.bitmap.height; y++) {\n        rsum = this.bitmap.data[yw] * rad1;\n        gsum = this.bitmap.data[yw + 1] * rad1;\n        bsum = this.bitmap.data[yw + 2] * rad1;\n        asum = this.bitmap.data[yw + 3] * rad1;\n\n        for (i = 1; i <= r; i++) {\n          p = yw + ((i > wm ? wm : i) << 2);\n          rsum += this.bitmap.data[p++];\n          gsum += this.bitmap.data[p++];\n          bsum += this.bitmap.data[p++];\n          asum += this.bitmap.data[p];\n        }\n\n        for (x = 0; x < this.bitmap.width; x++) {\n          red[yi] = rsum;\n          green[yi] = gsum;\n          blue[yi] = bsum;\n          alpha[yi] = asum;\n\n          if (y === 0) {\n            vmin[x] = ((p = x + rad1) < wm ? p : wm) << 2;\n            vmax[x] = (p = x - r) > 0 ? p << 2 : 0;\n          }\n\n          p1 = yw + vmin[x];\n          p2 = yw + vmax[x];\n\n          rsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n          gsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n          bsum += this.bitmap.data[p1++] - this.bitmap.data[p2++];\n          asum += this.bitmap.data[p1] - this.bitmap.data[p2];\n\n          yi++;\n        }\n\n        yw += this.bitmap.width << 2;\n      }\n\n      for (x = 0; x < this.bitmap.width; x++) {\n        yp = x;\n        rsum = red[yp] * rad1;\n        gsum = green[yp] * rad1;\n        bsum = blue[yp] * rad1;\n        asum = alpha[yp] * rad1;\n\n        for (i = 1; i <= r; i++) {\n          yp += i > hm ? 0 : this.bitmap.width;\n          rsum += red[yp];\n          gsum += green[yp];\n          bsum += blue[yp];\n          asum += alpha[yp];\n        }\n\n        yi = x << 2;\n\n        for (y = 0; y < this.bitmap.height; y++) {\n          pa = (asum * mulSum) >>> shgSum;\n          this.bitmap.data[yi + 3] = pa;\n\n          // normalize alpha\n          if (pa > 255) {\n            this.bitmap.data[yi + 3] = 255;\n          }\n\n          if (pa > 0) {\n            pa = 255 / pa;\n            this.bitmap.data[yi] = ((rsum * mulSum) >>> shgSum) * pa;\n            this.bitmap.data[yi + 1] = ((gsum * mulSum) >>> shgSum) * pa;\n            this.bitmap.data[yi + 2] = ((bsum * mulSum) >>> shgSum) * pa;\n          } else {\n            this.bitmap.data[yi + 2] = 0;\n            this.bitmap.data[yi + 1] = 0;\n            this.bitmap.data[yi] = 0;\n          }\n\n          if (x === 0) {\n            vmin[y] = ((p = y + rad1) < hm ? p : hm) * this.bitmap.width;\n            vmax[y] = (p = y - r) > 0 ? p * this.bitmap.width : 0;\n          }\n\n          p1 = x + vmin[y];\n          p2 = x + vmax[y];\n\n          rsum += red[p1] - red[p2];\n          gsum += green[p1] - green[p2];\n          bsum += blue[p1] - blue[p2];\n          asum += alpha[p1] - alpha[p2];\n\n          yi += this.bitmap.width << 2;\n        }\n      }\n    }\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n});\n"], "mappings": ";;;;;;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA,eA4Be,OAAO;EACpB;AACF;AACA;AACA;AACA;AACA;EACEA,IAAI,CAACC,CAAC,EAAEC,EAAE,EAAE;IACV,IAAI,OAAOD,CAAC,KAAK,QAAQ,EACvB,OAAOE,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEF,EAAE,CAAC;IACxD,IAAID,CAAC,GAAG,CAAC,EAAE,OAAOE,iBAAU,CAACC,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAEF,EAAE,CAAC;IAEvE,IAAIG,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IAEN,MAAMC,EAAE,GAAG,IAAI,CAACC,MAAM,CAACC,KAAK,GAAG,CAAC;IAChC,MAAMC,EAAE,GAAG,IAAI,CAACF,MAAM,CAACG,MAAM,GAAG,CAAC;IACjC;IACA,MAAMC,IAAI,GAAGvB,CAAC,GAAG,CAAC;IAElB,MAAMwB,MAAM,GAAGC,oBAAQ,CAACzB,CAAC,CAAC;IAC1B,MAAM0B,MAAM,GAAGC,oBAAQ,CAAC3B,CAAC,CAAC;IAE1B,MAAM4B,GAAG,GAAG,EAAE;IACd,MAAMC,KAAK,GAAG,EAAE;IAChB,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,KAAK,GAAG,EAAE;IAEhB,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,IAAI,GAAG,EAAE;IAEf,IAAIC,UAAU,GAAG,CAAC;IAElB,OAAOA,UAAU,EAAE,GAAG,CAAC,EAAE;MACvBnB,EAAE,GAAG,CAAC;MACNC,EAAE,GAAG,CAAC;MAEN,KAAKP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACU,MAAM,CAACG,MAAM,EAAEb,CAAC,EAAE,EAAE;QACvCL,IAAI,GAAG,IAAI,CAACe,MAAM,CAACgB,IAAI,CAACnB,EAAE,CAAC,GAAGO,IAAI;QAClClB,IAAI,GAAG,IAAI,CAACc,MAAM,CAACgB,IAAI,CAACnB,EAAE,GAAG,CAAC,CAAC,GAAGO,IAAI;QACtCjB,IAAI,GAAG,IAAI,CAACa,MAAM,CAACgB,IAAI,CAACnB,EAAE,GAAG,CAAC,CAAC,GAAGO,IAAI;QACtChB,IAAI,GAAG,IAAI,CAACY,MAAM,CAACgB,IAAI,CAACnB,EAAE,GAAG,CAAC,CAAC,GAAGO,IAAI;QAEtC,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIV,CAAC,EAAEU,CAAC,EAAE,EAAE;UACvBC,CAAC,GAAGK,EAAE,IAAI,CAACN,CAAC,GAAGQ,EAAE,GAAGA,EAAE,GAAGR,CAAC,KAAK,CAAC,CAAC;UACjCN,IAAI,IAAI,IAAI,CAACe,MAAM,CAACgB,IAAI,CAACxB,CAAC,EAAE,CAAC;UAC7BN,IAAI,IAAI,IAAI,CAACc,MAAM,CAACgB,IAAI,CAACxB,CAAC,EAAE,CAAC;UAC7BL,IAAI,IAAI,IAAI,CAACa,MAAM,CAACgB,IAAI,CAACxB,CAAC,EAAE,CAAC;UAC7BJ,IAAI,IAAI,IAAI,CAACY,MAAM,CAACgB,IAAI,CAACxB,CAAC,CAAC;QAC7B;QAEA,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACW,MAAM,CAACC,KAAK,EAAEZ,CAAC,EAAE,EAAE;UACtCoB,GAAG,CAACb,EAAE,CAAC,GAAGX,IAAI;UACdyB,KAAK,CAACd,EAAE,CAAC,GAAGV,IAAI;UAChByB,IAAI,CAACf,EAAE,CAAC,GAAGT,IAAI;UACfyB,KAAK,CAAChB,EAAE,CAAC,GAAGR,IAAI;UAEhB,IAAIE,CAAC,KAAK,CAAC,EAAE;YACXuB,IAAI,CAACxB,CAAC,CAAC,GAAG,CAAC,CAACG,CAAC,GAAGH,CAAC,GAAGe,IAAI,IAAIL,EAAE,GAAGP,CAAC,GAAGO,EAAE,KAAK,CAAC;YAC7Ce,IAAI,CAACzB,CAAC,CAAC,GAAG,CAACG,CAAC,GAAGH,CAAC,GAAGR,CAAC,IAAI,CAAC,GAAGW,CAAC,IAAI,CAAC,GAAG,CAAC;UACxC;UAEAC,EAAE,GAAGI,EAAE,GAAGgB,IAAI,CAACxB,CAAC,CAAC;UACjBK,EAAE,GAAGG,EAAE,GAAGiB,IAAI,CAACzB,CAAC,CAAC;UAEjBJ,IAAI,IAAI,IAAI,CAACe,MAAM,CAACgB,IAAI,CAACvB,EAAE,EAAE,CAAC,GAAG,IAAI,CAACO,MAAM,CAACgB,IAAI,CAACtB,EAAE,EAAE,CAAC;UACvDR,IAAI,IAAI,IAAI,CAACc,MAAM,CAACgB,IAAI,CAACvB,EAAE,EAAE,CAAC,GAAG,IAAI,CAACO,MAAM,CAACgB,IAAI,CAACtB,EAAE,EAAE,CAAC;UACvDP,IAAI,IAAI,IAAI,CAACa,MAAM,CAACgB,IAAI,CAACvB,EAAE,EAAE,CAAC,GAAG,IAAI,CAACO,MAAM,CAACgB,IAAI,CAACtB,EAAE,EAAE,CAAC;UACvDN,IAAI,IAAI,IAAI,CAACY,MAAM,CAACgB,IAAI,CAACvB,EAAE,CAAC,GAAG,IAAI,CAACO,MAAM,CAACgB,IAAI,CAACtB,EAAE,CAAC;UAEnDE,EAAE,EAAE;QACN;QAEAC,EAAE,IAAI,IAAI,CAACG,MAAM,CAACC,KAAK,IAAI,CAAC;MAC9B;MAEA,KAAKZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACW,MAAM,CAACC,KAAK,EAAEZ,CAAC,EAAE,EAAE;QACtCM,EAAE,GAAGN,CAAC;QACNJ,IAAI,GAAGwB,GAAG,CAACd,EAAE,CAAC,GAAGS,IAAI;QACrBlB,IAAI,GAAGwB,KAAK,CAACf,EAAE,CAAC,GAAGS,IAAI;QACvBjB,IAAI,GAAGwB,IAAI,CAAChB,EAAE,CAAC,GAAGS,IAAI;QACtBhB,IAAI,GAAGwB,KAAK,CAACjB,EAAE,CAAC,GAAGS,IAAI;QAEvB,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIV,CAAC,EAAEU,CAAC,EAAE,EAAE;UACvBI,EAAE,IAAIJ,CAAC,GAAGW,EAAE,GAAG,CAAC,GAAG,IAAI,CAACF,MAAM,CAACC,KAAK;UACpChB,IAAI,IAAIwB,GAAG,CAACd,EAAE,CAAC;UACfT,IAAI,IAAIwB,KAAK,CAACf,EAAE,CAAC;UACjBR,IAAI,IAAIwB,IAAI,CAAChB,EAAE,CAAC;UAChBP,IAAI,IAAIwB,KAAK,CAACjB,EAAE,CAAC;QACnB;QAEAC,EAAE,GAAGP,CAAC,IAAI,CAAC;QAEX,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACU,MAAM,CAACG,MAAM,EAAEb,CAAC,EAAE,EAAE;UACvCQ,EAAE,GAAIV,IAAI,GAAGiB,MAAM,KAAME,MAAM;UAC/B,IAAI,CAACP,MAAM,CAACgB,IAAI,CAACpB,EAAE,GAAG,CAAC,CAAC,GAAGE,EAAE;;UAE7B;UACA,IAAIA,EAAE,GAAG,GAAG,EAAE;YACZ,IAAI,CAACE,MAAM,CAACgB,IAAI,CAACpB,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG;UAChC;UAEA,IAAIE,EAAE,GAAG,CAAC,EAAE;YACVA,EAAE,GAAG,GAAG,GAAGA,EAAE;YACb,IAAI,CAACE,MAAM,CAACgB,IAAI,CAACpB,EAAE,CAAC,GAAG,CAAEX,IAAI,GAAGoB,MAAM,KAAME,MAAM,IAAIT,EAAE;YACxD,IAAI,CAACE,MAAM,CAACgB,IAAI,CAACpB,EAAE,GAAG,CAAC,CAAC,GAAG,CAAEV,IAAI,GAAGmB,MAAM,KAAME,MAAM,IAAIT,EAAE;YAC5D,IAAI,CAACE,MAAM,CAACgB,IAAI,CAACpB,EAAE,GAAG,CAAC,CAAC,GAAG,CAAET,IAAI,GAAGkB,MAAM,KAAME,MAAM,IAAIT,EAAE;UAC9D,CAAC,MAAM;YACL,IAAI,CAACE,MAAM,CAACgB,IAAI,CAACpB,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;YAC5B,IAAI,CAACI,MAAM,CAACgB,IAAI,CAACpB,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC;YAC5B,IAAI,CAACI,MAAM,CAACgB,IAAI,CAACpB,EAAE,CAAC,GAAG,CAAC;UAC1B;UAEA,IAAIP,CAAC,KAAK,CAAC,EAAE;YACXwB,IAAI,CAACvB,CAAC,CAAC,GAAG,CAAC,CAACE,CAAC,GAAGF,CAAC,GAAGc,IAAI,IAAIF,EAAE,GAAGV,CAAC,GAAGU,EAAE,IAAI,IAAI,CAACF,MAAM,CAACC,KAAK;YAC5Da,IAAI,CAACxB,CAAC,CAAC,GAAG,CAACE,CAAC,GAAGF,CAAC,GAAGT,CAAC,IAAI,CAAC,GAAGW,CAAC,GAAG,IAAI,CAACQ,MAAM,CAACC,KAAK,GAAG,CAAC;UACvD;UAEAR,EAAE,GAAGJ,CAAC,GAAGwB,IAAI,CAACvB,CAAC,CAAC;UAChBI,EAAE,GAAGL,CAAC,GAAGyB,IAAI,CAACxB,CAAC,CAAC;UAEhBL,IAAI,IAAIwB,GAAG,CAAChB,EAAE,CAAC,GAAGgB,GAAG,CAACf,EAAE,CAAC;UACzBR,IAAI,IAAIwB,KAAK,CAACjB,EAAE,CAAC,GAAGiB,KAAK,CAAChB,EAAE,CAAC;UAC7BP,IAAI,IAAIwB,IAAI,CAAClB,EAAE,CAAC,GAAGkB,IAAI,CAACjB,EAAE,CAAC;UAC3BN,IAAI,IAAIwB,KAAK,CAACnB,EAAE,CAAC,GAAGmB,KAAK,CAAClB,EAAE,CAAC;UAE7BE,EAAE,IAAI,IAAI,CAACI,MAAM,CAACC,KAAK,IAAI,CAAC;QAC9B;MACF;IACF;IAEA,IAAI,IAAAgB,oBAAa,EAACnC,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACE,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}