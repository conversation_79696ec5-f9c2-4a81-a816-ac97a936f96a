{"version": 3, "file": "composite-modes.js", "names": ["srcOver", "src", "dst", "ops", "a", "r", "g", "b", "dstOver", "multiply", "sra", "sga", "sba", "dra", "dga", "dba", "add", "screen", "overlay", "darken", "Math", "min", "lighten", "max", "hardLight", "difference", "exclusion"], "sources": ["../../src/composite/composite-modes.js"], "sourcesContent": ["export function srcOver(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const r = (src.r * src.a + dst.r * dst.a * (1 - src.a)) / a;\n  const g = (src.g * src.a + dst.g * dst.a * (1 - src.a)) / a;\n  const b = (src.b * src.a + dst.b * dst.a * (1 - src.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function dstOver(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const r = (dst.r * dst.a + src.r * src.a * (1 - dst.a)) / a;\n  const g = (dst.g * dst.a + src.g * src.a * (1 - dst.a)) / a;\n  const b = (dst.b * dst.a + src.b * src.a * (1 - dst.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function multiply(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r = (sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)) / a;\n  const g = (sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)) / a;\n  const b = (sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function add(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r = (sra + dra) / a;\n  const g = (sga + dga) / a;\n  const b = (sba + dba) / a;\n\n  return { r, g, b, a };\n}\n\nexport function screen(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (sra * dst.a +\n      dra * src.a -\n      sra * dra +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (sga * dst.a +\n      dga * src.a -\n      sga * dga +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (sba * dst.a +\n      dba * src.a -\n      sba * dba +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function overlay(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (2 * dra <= dst.a\n      ? 2 * sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)\n      : sra * (1 + dst.a) + dra * (1 + src.a) - 2 * dra * sra - dst.a * src.a) /\n    a;\n\n  const g =\n    (2 * dga <= dst.a\n      ? 2 * sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)\n      : sga * (1 + dst.a) + dga * (1 + src.a) - 2 * dga * sga - dst.a * src.a) /\n    a;\n\n  const b =\n    (2 * dba <= dst.a\n      ? 2 * sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)\n      : sba * (1 + dst.a) + dba * (1 + src.a) - 2 * dba * sba - dst.a * src.a) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function darken(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (Math.min(sra * dst.a, dra * src.a) +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (Math.min(sga * dst.a, dga * src.a) +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (Math.min(sba * dst.a, dba * src.a) +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function lighten(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (Math.max(sra * dst.a, dra * src.a) +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (Math.max(sga * dst.a, dga * src.a) +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (Math.max(sba * dst.a, dba * src.a) +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function hardLight(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (2 * sra <= src.a\n      ? 2 * sra * dra + sra * (1 - dst.a) + dra * (1 - src.a)\n      : sra * (1 + dst.a) + dra * (1 + src.a) - 2 * dra * sra - dst.a * src.a) /\n    a;\n\n  const g =\n    (2 * sga <= src.a\n      ? 2 * sga * dga + sga * (1 - dst.a) + dga * (1 - src.a)\n      : sga * (1 + dst.a) + dga * (1 + src.a) - 2 * dga * sga - dst.a * src.a) /\n    a;\n\n  const b =\n    (2 * sba <= src.a\n      ? 2 * sba * dba + sba * (1 - dst.a) + dba * (1 - src.a)\n      : sba * (1 + dst.a) + dba * (1 + src.a) - 2 * dba * sba - dst.a * src.a) /\n    a;\n\n  return { r, g, b, a };\n}\n\nexport function difference(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r = (sra + dra - 2 * Math.min(sra * dst.a, dra * src.a)) / a;\n  const g = (sga + dga - 2 * Math.min(sga * dst.a, dga * src.a)) / a;\n  const b = (sba + dba - 2 * Math.min(sba * dst.a, dba * src.a)) / a;\n\n  return { r, g, b, a };\n}\n\nexport function exclusion(src, dst, ops = 1) {\n  src.a *= ops;\n\n  const a = dst.a + src.a - dst.a * src.a;\n\n  const sra = src.r * src.a;\n  const sga = src.g * src.a;\n  const sba = src.b * src.a;\n\n  const dra = dst.r * dst.a;\n  const dga = dst.g * dst.a;\n  const dba = dst.b * dst.a;\n\n  const r =\n    (sra * dst.a +\n      dra * src.a -\n      2 * sra * dra +\n      sra * (1 - dst.a) +\n      dra * (1 - src.a)) /\n    a;\n  const g =\n    (sga * dst.a +\n      dga * src.a -\n      2 * sga * dga +\n      sga * (1 - dst.a) +\n      dga * (1 - src.a)) /\n    a;\n  const b =\n    (sba * dst.a +\n      dba * src.a -\n      2 * sba * dba +\n      sba * (1 - dst.a) +\n      dba * (1 - src.a)) /\n    a;\n\n  return { r, g, b, a };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAO,SAASA,OAAO,CAACC,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACvCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMC,CAAC,GAAG,CAACJ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC,IAAI,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EAC3D,MAAME,CAAC,GAAG,CAACL,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC,IAAI,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EAC3D,MAAMG,CAAC,GAAG,CAACN,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC,IAAI,CAAC,GAAGH,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EAE3D,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASI,OAAO,CAACP,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACvCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMC,CAAC,GAAG,CAACH,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC,IAAI,CAAC,GAAGF,GAAG,CAACE,CAAC,CAAC,IAAIA,CAAC;EAC3D,MAAME,CAAC,GAAG,CAACJ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC,IAAI,CAAC,GAAGF,GAAG,CAACE,CAAC,CAAC,IAAIA,CAAC;EAC3D,MAAMG,CAAC,GAAG,CAACL,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC,IAAI,CAAC,GAAGF,GAAG,CAACE,CAAC,CAAC,IAAIA,CAAC;EAE3D,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASK,QAAQ,CAACR,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACxCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GAAGS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EACjE,MAAME,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GAAGU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EACjE,MAAMG,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GAAGW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EAEjE,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASY,GAAG,CAACf,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACnCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,IAAIT,CAAC;EACzB,MAAME,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,IAAIV,CAAC;EACzB,MAAMG,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,IAAIX,CAAC;EAEzB,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASa,MAAM,CAAChB,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACtCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GACL,CAACK,GAAG,GAAGR,GAAG,CAACE,CAAC,GACVS,GAAG,GAAGZ,GAAG,CAACG,CAAC,GACXM,GAAG,GAAGG,GAAG,GACTH,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GACjBS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAME,CAAC,GACL,CAACK,GAAG,GAAGT,GAAG,CAACE,CAAC,GACVU,GAAG,GAAGb,GAAG,CAACG,CAAC,GACXO,GAAG,GAAGG,GAAG,GACTH,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GACjBU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAMG,CAAC,GACL,CAACK,GAAG,GAAGV,GAAG,CAACE,CAAC,GACVW,GAAG,GAAGd,GAAG,CAACG,CAAC,GACXQ,GAAG,GAAGG,GAAG,GACTH,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GACjBW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EAEH,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASc,OAAO,CAACjB,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACvCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GACL,CAAC,CAAC,GAAGQ,GAAG,IAAIX,GAAG,CAACE,CAAC,GACb,CAAC,GAAGM,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GAAGS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,GACrDM,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GAAGS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,GAAG,CAAC,GAAGS,GAAG,GAAGH,GAAG,GAAGR,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,IACzEA,CAAC;EAEH,MAAME,CAAC,GACL,CAAC,CAAC,GAAGQ,GAAG,IAAIZ,GAAG,CAACE,CAAC,GACb,CAAC,GAAGO,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GAAGU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,GACrDO,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GAAGU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,GAAG,CAAC,GAAGU,GAAG,GAAGH,GAAG,GAAGT,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,IACzEA,CAAC;EAEH,MAAMG,CAAC,GACL,CAAC,CAAC,GAAGQ,GAAG,IAAIb,GAAG,CAACE,CAAC,GACb,CAAC,GAAGQ,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GAAGW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,GACrDQ,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GAAGW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,GAAG,CAAC,GAAGW,GAAG,GAAGH,GAAG,GAAGV,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,IACzEA,CAAC;EAEH,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASe,MAAM,CAAClB,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACtCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GACL,CAACe,IAAI,CAACC,GAAG,CAACX,GAAG,GAAGR,GAAG,CAACE,CAAC,EAAES,GAAG,GAAGZ,GAAG,CAACG,CAAC,CAAC,GACjCM,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GACjBS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAME,CAAC,GACL,CAACc,IAAI,CAACC,GAAG,CAACV,GAAG,GAAGT,GAAG,CAACE,CAAC,EAAEU,GAAG,GAAGb,GAAG,CAACG,CAAC,CAAC,GACjCO,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GACjBU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAMG,CAAC,GACL,CAACa,IAAI,CAACC,GAAG,CAACT,GAAG,GAAGV,GAAG,CAACE,CAAC,EAAEW,GAAG,GAAGd,GAAG,CAACG,CAAC,CAAC,GACjCQ,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GACjBW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EAEH,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASkB,OAAO,CAACrB,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACvCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GACL,CAACe,IAAI,CAACG,GAAG,CAACb,GAAG,GAAGR,GAAG,CAACE,CAAC,EAAES,GAAG,GAAGZ,GAAG,CAACG,CAAC,CAAC,GACjCM,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GACjBS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAME,CAAC,GACL,CAACc,IAAI,CAACG,GAAG,CAACZ,GAAG,GAAGT,GAAG,CAACE,CAAC,EAAEU,GAAG,GAAGb,GAAG,CAACG,CAAC,CAAC,GACjCO,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GACjBU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAMG,CAAC,GACL,CAACa,IAAI,CAACG,GAAG,CAACX,GAAG,GAAGV,GAAG,CAACE,CAAC,EAAEW,GAAG,GAAGd,GAAG,CAACG,CAAC,CAAC,GACjCQ,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GACjBW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EAEH,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASoB,SAAS,CAACvB,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACzCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GACL,CAAC,CAAC,GAAGK,GAAG,IAAIT,GAAG,CAACG,CAAC,GACb,CAAC,GAAGM,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GAAGS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,GACrDM,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GAAGS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,GAAG,CAAC,GAAGS,GAAG,GAAGH,GAAG,GAAGR,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,IACzEA,CAAC;EAEH,MAAME,CAAC,GACL,CAAC,CAAC,GAAGK,GAAG,IAAIV,GAAG,CAACG,CAAC,GACb,CAAC,GAAGO,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GAAGU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,GACrDO,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GAAGU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,GAAG,CAAC,GAAGU,GAAG,GAAGH,GAAG,GAAGT,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,IACzEA,CAAC;EAEH,MAAMG,CAAC,GACL,CAAC,CAAC,GAAGK,GAAG,IAAIX,GAAG,CAACG,CAAC,GACb,CAAC,GAAGQ,GAAG,GAAGG,GAAG,GAAGH,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GAAGW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,GACrDQ,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GAAGW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,GAAG,CAAC,GAAGW,GAAG,GAAGH,GAAG,GAAGV,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,IACzEA,CAAC;EAEH,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASqB,UAAU,CAACxB,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EAC1CF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,GAAG,CAAC,GAAGO,IAAI,CAACC,GAAG,CAACX,GAAG,GAAGR,GAAG,CAACE,CAAC,EAAES,GAAG,GAAGZ,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EAClE,MAAME,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,GAAG,CAAC,GAAGM,IAAI,CAACC,GAAG,CAACV,GAAG,GAAGT,GAAG,CAACE,CAAC,EAAEU,GAAG,GAAGb,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EAClE,MAAMG,CAAC,GAAG,CAACK,GAAG,GAAGG,GAAG,GAAG,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACT,GAAG,GAAGV,GAAG,CAACE,CAAC,EAAEW,GAAG,GAAGd,GAAG,CAACG,CAAC,CAAC,IAAIA,CAAC;EAElE,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB;AAEO,SAASsB,SAAS,CAACzB,GAAG,EAAEC,GAAG,EAAW;EAAA,IAATC,GAAG,uEAAG,CAAC;EACzCF,GAAG,CAACG,CAAC,IAAID,GAAG;EAEZ,MAAMC,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,GAAGF,GAAG,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC;EAEvC,MAAMM,GAAG,GAAGT,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACG,CAAC;EACzB,MAAMO,GAAG,GAAGV,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACG,CAAC;EACzB,MAAMQ,GAAG,GAAGX,GAAG,CAACM,CAAC,GAAGN,GAAG,CAACG,CAAC;EAEzB,MAAMS,GAAG,GAAGX,GAAG,CAACG,CAAC,GAAGH,GAAG,CAACE,CAAC;EACzB,MAAMU,GAAG,GAAGZ,GAAG,CAACI,CAAC,GAAGJ,GAAG,CAACE,CAAC;EACzB,MAAMW,GAAG,GAAGb,GAAG,CAACK,CAAC,GAAGL,GAAG,CAACE,CAAC;EAEzB,MAAMC,CAAC,GACL,CAACK,GAAG,GAAGR,GAAG,CAACE,CAAC,GACVS,GAAG,GAAGZ,GAAG,CAACG,CAAC,GACX,CAAC,GAAGM,GAAG,GAAGG,GAAG,GACbH,GAAG,IAAI,CAAC,GAAGR,GAAG,CAACE,CAAC,CAAC,GACjBS,GAAG,IAAI,CAAC,GAAGZ,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAME,CAAC,GACL,CAACK,GAAG,GAAGT,GAAG,CAACE,CAAC,GACVU,GAAG,GAAGb,GAAG,CAACG,CAAC,GACX,CAAC,GAAGO,GAAG,GAAGG,GAAG,GACbH,GAAG,IAAI,CAAC,GAAGT,GAAG,CAACE,CAAC,CAAC,GACjBU,GAAG,IAAI,CAAC,GAAGb,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EACH,MAAMG,CAAC,GACL,CAACK,GAAG,GAAGV,GAAG,CAACE,CAAC,GACVW,GAAG,GAAGd,GAAG,CAACG,CAAC,GACX,CAAC,GAAGQ,GAAG,GAAGG,GAAG,GACbH,GAAG,IAAI,CAAC,GAAGV,GAAG,CAACE,CAAC,CAAC,GACjBW,GAAG,IAAI,CAAC,GAAGd,GAAG,CAACG,CAAC,CAAC,IACnBA,CAAC;EAEH,OAAO;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEH;EAAE,CAAC;AACvB"}