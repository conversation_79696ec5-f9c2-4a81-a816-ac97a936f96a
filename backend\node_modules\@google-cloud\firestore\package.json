{"name": "@google-cloud/firestore", "description": "Firestore Client Library for Node.js", "version": "7.11.1", "license": "Apache-2.0", "author": "Google Inc.", "engines": {"node": ">=14.0.0"}, "repository": "googleapis/nodejs-firestore", "main": "./build/src/index.js", "types": "./types/firestore.d.ts", "files": ["build/protos", "build/src", "!build/src/**/*.map", "types"], "keywords": ["google apis client", "google api client", "google apis", "google api", "google", "google cloud platform", "google cloud", "cloud", "firestore"], "scripts": {"precloud-rad": "npm run compile", "cloud-rad": "NO_UPLOAD=1 npx @google-cloud/cloud-rad", "preapi-report": "npm run compile", "api-report": "node scripts/api-report.js", "predocs": "npm run compile", "docs": "jsdoc -c .jsdoc.js", "system-test:rest": "FIRESTORE_PREFER_REST=true mocha build/system-test --timeout 1200000", "system-test:named-db:rest": "FIRESTORE_NAMED_DATABASE=test-db FIRESTORE_PREFER_REST=true mocha build/system-test --timeout 1200000", "system-test:grpc": "mocha build/system-test --timeout 1200000", "system-test:named-db:grpc": "FIRESTORE_NAMED_DATABASE=test-db mocha build/system-test --timeout 1200000", "system-test:emulator:rest": "FIRESTORE_EMULATOR_HOST=localhost:8080 FIRESTORE_PREFER_REST=true mocha build/system-test --timeout 1200000", "system-test:named-db:emulator:rest": "FIRESTORE_NAMED_DATABASE=test-db FIRESTORE_EMULATOR_HOST=localhost:8080 FIRESTORE_PREFER_REST=true mocha build/system-test --timeout 1200000", "system-test:emulator:grpc": "FIRESTORE_EMULATOR_HOST=localhost:8080 mocha build/system-test --timeout 1200000", "system-test:named-db:emulator:grpc": "FIRESTORE_NAMED_DATABASE=test-db FIRESTORE_EMULATOR_HOST=localhost:8080 mocha build/system-test --timeout 1200000", "system-test": "npm run system-test:grpc && npm run system-test:rest && npm run system-test:named-db:grpc && npm run system-test:named-db:rest", "system-test:emulator": "npm run system-test:emulator:grpc && npm run system-test:emulator:rest && npm run system-test:named-db:emulator:grpc && npm run system-test:named-db:emulator:rest", "presystem-test": "npm run compile", "samples-test": "npm link && cd samples/ && npm link ../ && npm test && cd ../", "conformance": "mocha build/conformance", "preconformance": "npm run compile", "test-only": "c8 mocha build/test", "pretest-only": "npm run compile", "test": "npm run test-only && npm run conformance", "lint": "gts check", "clean": "gts clean", "compile": "tsc -p .", "postcompile": "node scripts/init-directories.js && cp -r dev/protos build && cp dev/src/v1beta1/*.json build/src/v1beta1/ && cp dev/src/v1/*.json build/src/v1/ && cp dev/conformance/test-definition.proto build/conformance && cp dev/conformance/conformance-tests/*.json build/conformance/conformance-tests && minifyProtoJson", "fix": "gts fix", "prepare": "npm run compile", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean"}, "dependencies": {"@opentelemetry/api": "^1.3.0", "fast-deep-equal": "^3.1.1", "functional-red-black-tree": "^1.0.1", "google-gax": "^4.3.3", "protobufjs": "^7.2.6"}, "devDependencies": {"@google-cloud/promisify": "legacy-14", "@google-cloud/trace-agent": "^8.0.0", "@googleapis/cloudtrace": "^1.1.2", "@google-cloud/cloud-rad": "^0.4.0", "@google-cloud/opentelemetry-cloud-trace-exporter": "^2.0.0", "@opentelemetry/context-async-hooks": "^1.24.1", "@opentelemetry/sdk-trace-node": "^1.24.1", "@types/assert": "^1.4.0", "@types/chai": "^4.2.7", "@types/chai-as-promised": "^7.1.2", "@types/duplexify": "^3.5.0", "@types/extend": "^3.0.0", "@types/mocha": "^9.0.0", "@types/node": "^22.0.0", "@types/sinon": "^17.0.0", "@types/through2": "^2.0.34", "c8": "^9.0.0", "chai": "^4.1.2", "chai-as-promised": "^7.1.1", "codecov": "^3.6.1", "duplexify": "^4.0.0", "execa": "^5.1.1", "extend": "^3.0.2", "fs-extra": "10.1.0", "gapic-tools": "^0.4.0", "gts": "^5.0.1", "jsdoc": "^4.0.0", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "length-prefixed-json-stream": "^1.0.1", "linkinator": "^3.0.0", "mkdirp": "^3.0.0", "mocha": "^9.2.2", "protobufjs-cli": "^1.1.2", "proxyquire": "^2.1.3", "nise": "6.0.0", "sinon": "^18.0.0", "path-to-regexp": "^6.0.0", "through2": "^4.0.0", "ts-node": "^10.0.0", "typescript": "^5.2.2"}}