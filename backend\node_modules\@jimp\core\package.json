{"name": "@jimp/core", "version": "0.22.12", "description": "<PERSON><PERSON> core", "main": "dist/index.js", "module": "es/index.js", "types": "types/index.d.ts", "sideEffects": false, "files": ["dist", "es", "index.d.ts", "fonts", "types"], "repository": "jimp-dev/jimp", "scripts": {"test": "cross-env BABEL_ENV=test mocha --require @babel/register --recursive test --extension js", "test:watch": "npm run test -- --reporter min --watch", "test:coverage": "nyc npm run test", "build": "npm run build:node:production && npm run build:module", "build:watch": "npm run build:node:debug -- -- --watch --verbose", "build:debug": "npm run build:node:debug", "build:module": "cross-env BABEL_ENV=module babel src -d es --source-maps --config-file ../../babel.config.js", "build:node": "babel src -d dist --source-maps --config-file ../../babel.config.js", "build:node:debug": "cross-env BABEL_ENV=development npm run build:node", "build:node:production": "cross-env BABEL_ENV=production npm run build:node"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@jimp/utils": "^0.22.12", "any-base": "^1.1.0", "buffer": "^5.2.0", "exif-parser": "^0.1.12", "file-type": "^16.5.4", "isomorphic-fetch": "^3.0.0", "pixelmatch": "^4.0.2", "tinycolor2": "^1.6.0"}, "publishConfig": {"access": "public"}, "gitHead": "a4a8d6364bbf97629749e196f3b0a4c94c9a7abc"}