{"version": 3, "file": "index.js", "names": ["MIME_TYPE", "mime", "constants", "MIME_GIF", "decoders", "data", "gifObj", "GIF", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gifData", "<PERSON><PERSON><PERSON>", "alloc", "width", "height", "decodeAndBlitFrameRGBA", "encoders", "bitmap", "BitmapImage", "<PERSON><PERSON><PERSON><PERSON>", "quantizeDekker", "newFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gifCodec", "GifCodec", "encodeGif", "then", "newGif", "buffer"], "sources": ["../src/index.js"], "sourcesContent": ["import GIF from \"omggif\";\nimport { GifUtil, GifFrame, BitmapImage, GifCodec } from \"gifwrap\";\n\nconst MIME_TYPE = \"image/gif\";\n\nexport default () => ({\n  mime: { [MIME_TYPE]: [\"gif\"] },\n\n  constants: {\n    MIME_GIF: MIME_TYPE,\n  },\n\n  decoders: {\n    [MIME_TYPE]: (data) => {\n      const gifObj = new GIF.GifReader(data);\n      const gifData = Buffer.alloc(gifObj.width * gifObj.height * 4);\n\n      gifObj.decodeAndBlitFrameRGBA(0, gifData);\n\n      return {\n        data: gifData,\n        width: gifObj.width,\n        height: gifObj.height,\n      };\n    },\n  },\n\n  encoders: {\n    [MIME_TYPE]: (data) => {\n      const bitmap = new BitmapImage(data.bitmap);\n      GifUtil.quantizeDekker(bitmap, 256);\n      const newFrame = new GifFrame(bitmap);\n      const gifCodec = new GifCodec();\n      return gifCodec.encodeGif([newFrame], {}).then((newGif) => {\n        return newGif.buffer;\n      });\n    },\n  },\n});\n"], "mappings": ";;;;;;AAAA;AACA;AAAmE;AAEnE,MAAMA,SAAS,GAAG,WAAW;AAAC,eAEf,OAAO;EACpBC,IAAI,EAAE;IAAE,CAACD,SAAS,GAAG,CAAC,KAAK;EAAE,CAAC;EAE9BE,SAAS,EAAE;IACTC,QAAQ,EAAEH;EACZ,CAAC;EAEDI,QAAQ,EAAE;IACR,CAACJ,SAAS,GAAIK,IAAI,IAAK;MACrB,MAAMC,MAAM,GAAG,IAAIC,eAAG,CAACC,SAAS,CAACH,IAAI,CAAC;MACtC,MAAMI,OAAO,GAAGC,MAAM,CAACC,KAAK,CAACL,MAAM,CAACM,KAAK,GAAGN,MAAM,CAACO,MAAM,GAAG,CAAC,CAAC;MAE9DP,MAAM,CAACQ,sBAAsB,CAAC,CAAC,EAAEL,OAAO,CAAC;MAEzC,OAAO;QACLJ,IAAI,EAAEI,OAAO;QACbG,KAAK,EAAEN,MAAM,CAACM,KAAK;QACnBC,MAAM,EAAEP,MAAM,CAACO;MACjB,CAAC;IACH;EACF,CAAC;EAEDE,QAAQ,EAAE;IACR,CAACf,SAAS,GAAIK,IAAI,IAAK;MACrB,MAAMW,MAAM,GAAG,IAAIC,oBAAW,CAACZ,IAAI,CAACW,MAAM,CAAC;MAC3CE,gBAAO,CAACC,cAAc,CAACH,MAAM,EAAE,GAAG,CAAC;MACnC,MAAMI,QAAQ,GAAG,IAAIC,iBAAQ,CAACL,MAAM,CAAC;MACrC,MAAMM,QAAQ,GAAG,IAAIC,iBAAQ,EAAE;MAC/B,OAAOD,QAAQ,CAACE,SAAS,CAAC,CAACJ,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,CAACK,IAAI,CAAEC,MAAM,IAAK;QACzD,OAAOA,MAAM,CAACC,MAAM;MACtB,CAAC,CAAC;IACJ;EACF;AACF,CAAC,CAAC;AAAA;AAAA;AAAA"}