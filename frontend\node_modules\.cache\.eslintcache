[{"C:\\Users\\<USER>\\ebayali\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\App.jsx": "2", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\hooks\\useToast.js": "3", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\ToastContainer.jsx": "4", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\UploadScreen.jsx": "5", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\VariantsTab.jsx": "6", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\hooks\\useVariants.js": "7", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\ActionBar.jsx": "8", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\VariantsTable.jsx": "9", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\ProductHeader.jsx": "10", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\hooks\\useAuth.js": "11", "C:\\Users\\<USER>\\ebayali\\frontend\\src\\config\\firebase.js": "12"}, {"size": 1132, "mtime": 1749773139443, "results": "13", "hashOfConfig": "14"}, {"size": 978, "mtime": 1749776315299, "results": "15", "hashOfConfig": "14"}, {"size": 866, "mtime": 1749764642050, "results": "16", "hashOfConfig": "14"}, {"size": 2232, "mtime": 1749764658068, "results": "17", "hashOfConfig": "14"}, {"size": 7373, "mtime": 1749777875323, "results": "18", "hashOfConfig": "14"}, {"size": 7841, "mtime": 1749764509427, "results": "19", "hashOfConfig": "14"}, {"size": 7858, "mtime": 1749778088523, "results": "20", "hashOfConfig": "14"}, {"size": 4470, "mtime": 1749764531465, "results": "21", "hashOfConfig": "14"}, {"size": 9469, "mtime": 1749764592339, "results": "22", "hashOfConfig": "14"}, {"size": 3945, "mtime": 1749764553864, "results": "23", "hashOfConfig": "14"}, {"size": 9733, "mtime": 1749771507854, "results": "24", "hashOfConfig": "14"}, {"size": 1723, "mtime": 1749771470058, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1dyki1z", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\ebayali\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\hooks\\useToast.js", ["62"], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\ToastContainer.jsx", [], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\UploadScreen.jsx", ["63"], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\VariantsTab.jsx", ["64", "65", "66", "67", "68", "69", "70", "71", "72"], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\hooks\\useVariants.js", ["73"], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\ActionBar.jsx", [], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\VariantsTable.jsx", [], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\components\\ProductHeader.jsx", [], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\hooks\\useAuth.js", [], [], "C:\\Users\\<USER>\\ebayali\\frontend\\src\\config\\firebase.js", [], [], {"ruleId": "74", "severity": 1, "message": "75", "line": 25, "column": 6, "nodeType": "76", "endLine": 25, "endColumn": 8, "suggestions": "77"}, {"ruleId": "74", "severity": 1, "message": "78", "line": 26, "column": 6, "nodeType": "76", "endLine": 26, "endColumn": 8, "suggestions": "79"}, {"ruleId": "80", "severity": 1, "message": "81", "line": 3, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 3, "endColumn": 19}, {"ruleId": "80", "severity": 1, "message": "84", "line": 4, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 4, "endColumn": 12}, {"ruleId": "80", "severity": 1, "message": "85", "line": 6, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 6, "endColumn": 28}, {"ruleId": "80", "severity": 1, "message": "86", "line": 7, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 7, "endColumn": 11}, {"ruleId": "80", "severity": 1, "message": "87", "line": 8, "column": 3, "nodeType": "82", "messageId": "83", "endLine": 8, "endColumn": 15}, {"ruleId": "80", "severity": 1, "message": "88", "line": 25, "column": 5, "nodeType": "82", "messageId": "83", "endLine": 25, "endColumn": 16}, {"ruleId": "80", "severity": 1, "message": "89", "line": 31, "column": 5, "nodeType": "82", "messageId": "83", "endLine": 31, "endColumn": 18}, {"ruleId": "80", "severity": 1, "message": "90", "line": 33, "column": 5, "nodeType": "82", "messageId": "83", "endLine": 33, "endColumn": 23}, {"ruleId": "74", "severity": 1, "message": "91", "line": 51, "column": 6, "nodeType": "76", "endLine": 51, "endColumn": 32, "suggestions": "92"}, {"ruleId": "80", "severity": 1, "message": "93", "line": 5, "column": 9, "nodeType": "82", "messageId": "83", "endLine": 5, "endColumn": 23}, "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeToast'. Either include it or remove the dependency array.", "ArrayExpression", ["94"], "React Hook useEffect has a missing dependency: 'fetchUploadCounts'. Either include it or remove the dependency array.", ["95"], "no-unused-vars", "'PencilSquareIcon' is defined but never used.", "Identifier", "unusedVar", "'TrashIcon' is defined but never used.", "'ArrowTopRightOnSquareIcon' is defined but never used.", "'PlusIcon' is defined but never used.", "'SparklesIcon' is defined but never used.", "'productData' is assigned a value but never used.", "'updateVariant' is assigned a value but never used.", "'bulkUpdateVariants' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'fetchVariants' and 'sampleProduct.id'. Either include them or remove the dependency array.", ["96"], "'getAuthHeaders' is assigned a value but never used.", {"desc": "97", "fix": "98"}, {"desc": "99", "fix": "100"}, {"desc": "101", "fix": "102"}, "Update the dependencies array to be: [removeToast]", {"range": "103", "text": "104"}, "Update the dependencies array to be: [fetchUploadCounts]", {"range": "105", "text": "106"}, "Update the dependencies array to be: [currentPage, fetchVariants, rowsPerPage, sampleProduct.id]", {"range": "107", "text": "108"}, [564, 566], "[removeToast]", [742, 744], "[fetchUploadCounts]", [1602, 1628], "[currentPage, fetchVariants, rowsPerPage, sampleProduct.id]"]