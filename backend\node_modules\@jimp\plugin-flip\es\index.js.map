{"version": 3, "file": "index.js", "names": ["isNodePattern", "throwError", "flipFn", "horizontal", "vertical", "cb", "call", "bitmap", "<PERSON><PERSON><PERSON>", "alloc", "data", "length", "scanQuiet", "width", "height", "x", "y", "idx", "_x", "_y", "_idx", "readUInt32BE", "writeUInt32BE", "from", "flip", "mirror"], "sources": ["../src/index.js"], "sourcesContent": ["import { isNodePattern, throwError } from \"@jimp/utils\";\n\n/**\n * Flip the image horizontally\n * @param {boolean} horizontal a Boolean, if true the image will be flipped horizontally\n * @param {boolean} vertical a Boolean, if true the image will be flipped vertically\n * @param {function(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)} cb (optional) a callback for when complete\n * @returns {Jimp} this for chaining of methods\n */\nfunction flipFn(horizontal, vertical, cb) {\n  if (typeof horizontal !== \"boolean\" || typeof vertical !== \"boolean\")\n    return throwError.call(\n      this,\n      \"horizontal and vertical must be Booleans\",\n      cb\n    );\n\n  const bitmap = Buffer.alloc(this.bitmap.data.length);\n  this.scanQuiet(\n    0,\n    0,\n    this.bitmap.width,\n    this.bitmap.height,\n    function (x, y, idx) {\n      const _x = horizontal ? this.bitmap.width - 1 - x : x;\n      const _y = vertical ? this.bitmap.height - 1 - y : y;\n      const _idx = (this.bitmap.width * _y + _x) << 2;\n      const data = this.bitmap.data.readUInt32BE(idx);\n\n      bitmap.writeUInt32BE(data, _idx);\n    }\n  );\n\n  this.bitmap.data = Buffer.from(bitmap);\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nexport default () => ({\n  flip: flipFn,\n  mirror: flipFn,\n});\n"], "mappings": "AAAA,SAASA,aAAa,EAAEC,UAAU,QAAQ,aAAa;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAM,CAACC,UAAU,EAAEC,QAAQ,EAAEC,EAAE,EAAE;EACxC,IAAI,OAAOF,UAAU,KAAK,SAAS,IAAI,OAAOC,QAAQ,KAAK,SAAS,EAClE,OAAOH,UAAU,CAACK,IAAI,CACpB,IAAI,EACJ,0CAA0C,EAC1CD,EAAE,CACH;EAEH,MAAME,MAAM,GAAGC,MAAM,CAACC,KAAK,CAAC,IAAI,CAACF,MAAM,CAACG,IAAI,CAACC,MAAM,CAAC;EACpD,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACL,MAAM,CAACM,KAAK,EACjB,IAAI,CAACN,MAAM,CAACO,MAAM,EAClB,UAAUC,CAAC,EAAEC,CAAC,EAAEC,GAAG,EAAE;IACnB,MAAMC,EAAE,GAAGf,UAAU,GAAG,IAAI,CAACI,MAAM,CAACM,KAAK,GAAG,CAAC,GAAGE,CAAC,GAAGA,CAAC;IACrD,MAAMI,EAAE,GAAGf,QAAQ,GAAG,IAAI,CAACG,MAAM,CAACO,MAAM,GAAG,CAAC,GAAGE,CAAC,GAAGA,CAAC;IACpD,MAAMI,IAAI,GAAI,IAAI,CAACb,MAAM,CAACM,KAAK,GAAGM,EAAE,GAAGD,EAAE,IAAK,CAAC;IAC/C,MAAMR,IAAI,GAAG,IAAI,CAACH,MAAM,CAACG,IAAI,CAACW,YAAY,CAACJ,GAAG,CAAC;IAE/CV,MAAM,CAACe,aAAa,CAACZ,IAAI,EAAEU,IAAI,CAAC;EAClC,CAAC,CACF;EAED,IAAI,CAACb,MAAM,CAACG,IAAI,GAAGF,MAAM,CAACe,IAAI,CAAChB,MAAM,CAAC;EAEtC,IAAIP,aAAa,CAACK,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;AAEA,gBAAe,OAAO;EACpBkB,IAAI,EAAEtB,MAAM;EACZuB,MAAM,EAAEvB;AACV,CAAC,CAAC"}