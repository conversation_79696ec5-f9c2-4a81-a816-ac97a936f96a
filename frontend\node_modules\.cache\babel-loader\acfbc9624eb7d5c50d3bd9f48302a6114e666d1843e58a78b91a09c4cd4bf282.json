{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nconst useVariants = () => {\n  _s();\n  // Mock auth headers for now\n  const getAuthHeaders = () => ({\n    'Authorization': 'Bearer mock-token'\n  });\n  const [productData, setProductData] = useState(null);\n  const [variants, setVariants] = useState([]);\n  const [totalVariants, setTotalVariants] = useState(0);\n  const [isLoadingVariants, setIsLoadingVariants] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Generate mock variant data that matches our backend structure\n  const generateMockVariants = (count = 20) => {\n    const colors = [{\n      name: 'Black',\n      german: 'Schwarz'\n    }, {\n      name: 'White',\n      german: 'Weiß'\n    }, {\n      name: 'Blue',\n      german: 'Blau'\n    }, {\n      name: 'Red',\n      german: 'Rot'\n    }, {\n      name: '<PERSON>',\n      german: '<PERSON><PERSON><PERSON><PERSON>'\n    }, {\n      name: 'Pink',\n      german: 'Rosa'\n    }];\n    const materials = ['For iPhone 16E', 'For iPhone 15 Pro', 'For iPhone 14', 'For iPhone 13', 'For iPhone 12'];\n    const statuses = ['IN_STOCK', 'ON_HOLD', 'OUT_OF_STOCK'];\n    const mockVariants = [];\n    for (let i = 0; i < count; i++) {\n      const color = colors[i % colors.length];\n      const material = materials[i % materials.length];\n      const status = statuses[i % statuses.length];\n      mockVariants.push({\n        id: `variant_${Date.now()}_${i}`,\n        name: `${color.german} ${material}`,\n        color: color.german,\n        material: material,\n        size: null,\n        model: material,\n        price: 8.47 + i * 0.5,\n        currency: 'EUR',\n        status: status,\n        stock: Math.floor(Math.random() * 100) + 10,\n        buyId: `1005007038106701_771_${i}`,\n        sku: `HP-${color.name.toUpperCase()}-${i.toString().padStart(3, '0')}`,\n        image: `https://images.unsplash.com/photo-${1505740420928 + i}?w=100&h=100&fit=crop`,\n        images: [`https://images.unsplash.com/photo-${1505740420928 + i}?w=500`, `https://images.unsplash.com/photo-${1484704849700 + i}?w=500`],\n        options: [{\n          name: 'Farbe',\n          value: color.german,\n          originalValue: color.name\n        }, {\n          name: 'Material',\n          value: material,\n          originalValue: material\n        }]\n      });\n    }\n    return mockVariants;\n  };\n  const fetchVariants = useCallback(async (productId, page = 1, limit = 20) => {\n    setIsLoadingVariants(true);\n    setError(null);\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 800));\n\n      // In production, this would be an actual API call:\n      // const authHeaders = await getAuthHeaders();\n      // const response = await fetch(`/api/v1/products/${productId}/variants?page=${page}&limit=${limit}`, {\n      //   headers: {\n      //     'Content-Type': 'application/json',\n      //     ...authHeaders\n      //   }\n      // });\n      // const data = await response.json();\n\n      // For now, generate mock data\n      const mockVariants = generateMockVariants(limit);\n      const totalCount = 135; // Mock total count\n\n      setVariants(mockVariants);\n      setTotalVariants(totalCount);\n\n      // Mock product data\n      setProductData({\n        id: productId,\n        title: '3D Gold Herz Silikon Soft Phone Fall Für iPhone 15 14 13 12 11 16 Pro Max Xs Xr 7 8 plus Se Stoß Feste Süßigkeiten Matte Abdeckung',\n        enriched: true,\n        enrichmentSource: 'comprehensive_rapidapi',\n        locale: {\n          language: 'de',\n          currency: 'EUR',\n          region: 'DE'\n        }\n      });\n    } catch (err) {\n      setError(err.message || 'Failed to fetch variants');\n      console.error('Error fetching variants:', err);\n    } finally {\n      setIsLoadingVariants(false);\n    }\n  }, []);\n  const updateVariant = useCallback(async (variantId, updates) => {\n    try {\n      // In production, this would be an actual API call:\n      // const authHeaders = await getAuthHeaders();\n      // const response = await fetch(`/api/v1/variants/${variantId}`, {\n      //   method: 'PATCH',\n      //   headers: {\n      //     'Content-Type': 'application/json',\n      //     ...authHeaders\n      //   },\n      //   body: JSON.stringify(updates)\n      // });\n\n      // Mock update\n      setVariants(prev => prev.map(variant => variant.id === variantId ? {\n        ...variant,\n        ...updates\n      } : variant));\n      return {\n        success: true\n      };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to update variant');\n    }\n  }, []);\n  const deleteVariant = useCallback(async variantId => {\n    try {\n      // In production, this would be an actual API call:\n      // const response = await fetch(`/api/v1/variants/${variantId}`, {\n      //   method: 'DELETE'\n      // });\n\n      // Mock deletion\n      setVariants(prev => prev.filter(variant => variant.id !== variantId));\n      setTotalVariants(prev => prev - 1);\n      return {\n        success: true\n      };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to delete variant');\n    }\n  }, []);\n  const bulkUpdateVariants = useCallback(async (variantIds, updates) => {\n    try {\n      // In production, this would be an actual API call:\n      // const response = await fetch('/api/v1/variants/bulk-update', {\n      //   method: 'PATCH',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ variantIds, updates })\n      // });\n\n      // Mock bulk update\n      setVariants(prev => prev.map(variant => variantIds.includes(variant.id) ? {\n        ...variant,\n        ...updates\n      } : variant));\n      return {\n        success: true,\n        updatedCount: variantIds.length\n      };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to bulk update variants');\n    }\n  }, []);\n  const saveAndImportProduct = useCallback(async (productId, variantsToImport) => {\n    try {\n      // In production, this would call our comprehensive enrichment system:\n      // const response = await fetch('/api/v1/products/save-and-import', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({\n      //     productId,\n      //     variants: variantsToImport,\n      //     locale: { language: 'de', currency: 'EUR', region: 'DE' }\n      //   })\n      // });\n\n      // Mock save and import\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      return {\n        success: true,\n        importedCount: variantsToImport.length,\n        message: 'Product and variants imported successfully'\n      };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to save and import product');\n    }\n  }, []);\n  const addVariant = useCallback(async (productId, variantData) => {\n    try {\n      // In production, this would be an actual API call:\n      // const response = await fetch(`/api/v1/products/${productId}/variants`, {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(variantData)\n      // });\n\n      // Mock add variant\n      const newVariant = {\n        id: `variant_${Date.now()}_new`,\n        ...variantData,\n        status: 'IN_STOCK',\n        buyId: `new_${Date.now()}`,\n        price: variantData.price || 8.47,\n        currency: 'EUR'\n      };\n      setVariants(prev => [...prev, newVariant]);\n      setTotalVariants(prev => prev + 1);\n      return {\n        success: true,\n        variant: newVariant\n      };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to add variant');\n    }\n  }, []);\n  return {\n    productData,\n    variants,\n    totalVariants,\n    isLoadingVariants,\n    error,\n    fetchVariants,\n    updateVariant,\n    deleteVariant,\n    bulkUpdateVariants,\n    saveAndImportProduct,\n    addVariant\n  };\n};\n_s(useVariants, \"kx0HFnxVoWL61ddS0apADMGNJ80=\");\nexport { useVariants };", "map": {"version": 3, "names": ["useState", "useCallback", "useVariants", "_s", "getAuthHeaders", "productData", "setProductData", "variants", "setVariants", "totalVariants", "setTotalVariants", "isLoadingVariants", "setIsLoadingVariants", "error", "setError", "generateMockVariants", "count", "colors", "name", "german", "materials", "statuses", "mockVariants", "i", "color", "length", "material", "status", "push", "id", "Date", "now", "size", "model", "price", "currency", "stock", "Math", "floor", "random", "buyId", "sku", "toUpperCase", "toString", "padStart", "image", "images", "options", "value", "originalValue", "fetchVariants", "productId", "page", "limit", "Promise", "resolve", "setTimeout", "totalCount", "title", "enriched", "enrichmentSource", "locale", "language", "region", "err", "message", "console", "updateVariant", "variantId", "updates", "prev", "map", "variant", "success", "Error", "deleteVariant", "filter", "bulkUpdateVariants", "variantIds", "includes", "updatedCount", "saveAndImportProduct", "variantsToImport", "importedCount", "addVariant", "variantData", "newVariant"], "sources": ["C:/Users/<USER>/ebayali/frontend/src/hooks/useVariants.js"], "sourcesContent": ["import { useState, useCallback } from 'react';\n\nconst useVariants = () => {\n  // Mock auth headers for now\n  const getAuthHeaders = () => ({ 'Authorization': 'Bearer mock-token' });\n  const [productData, setProductData] = useState(null);\n  const [variants, setVariants] = useState([]);\n  const [totalVariants, setTotalVariants] = useState(0);\n  const [isLoadingVariants, setIsLoadingVariants] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Generate mock variant data that matches our backend structure\n  const generateMockVariants = (count = 20) => {\n    const colors = [\n      { name: 'Black', german: '<PERSON><PERSON><PERSON>' },\n      { name: 'White', german: 'Weiß' },\n      { name: 'Blue', german: 'Blau' },\n      { name: 'Red', german: 'Rot' },\n      { name: 'Green', german: '<PERSON>rü<PERSON>' },\n      { name: '<PERSON>', german: '<PERSON>' }\n    ];\n\n    const materials = [\n      'For iPhone 16E',\n      'For iPhone 15 Pro',\n      'For iPhone 14',\n      'For iPhone 13',\n      'For iPhone 12'\n    ];\n\n    const statuses = ['IN_STOCK', 'ON_HOLD', 'OUT_OF_STOCK'];\n    const mockVariants = [];\n\n    for (let i = 0; i < count; i++) {\n      const color = colors[i % colors.length];\n      const material = materials[i % materials.length];\n      const status = statuses[i % statuses.length];\n      \n      mockVariants.push({\n        id: `variant_${Date.now()}_${i}`,\n        name: `${color.german} ${material}`,\n        color: color.german,\n        material: material,\n        size: null,\n        model: material,\n        price: 8.47 + (i * 0.5),\n        currency: 'EUR',\n        status: status,\n        stock: Math.floor(Math.random() * 100) + 10,\n        buyId: `1005007038106701_771_${i}`,\n        sku: `HP-${color.name.toUpperCase()}-${i.toString().padStart(3, '0')}`,\n        image: `https://images.unsplash.com/photo-${1505740420928 + i}?w=100&h=100&fit=crop`,\n        images: [\n          `https://images.unsplash.com/photo-${1505740420928 + i}?w=500`,\n          `https://images.unsplash.com/photo-${1484704849700 + i}?w=500`\n        ],\n        options: [\n          {\n            name: 'Farbe',\n            value: color.german,\n            originalValue: color.name\n          },\n          {\n            name: 'Material',\n            value: material,\n            originalValue: material\n          }\n        ]\n      });\n    }\n\n    return mockVariants;\n  };\n\n  const fetchVariants = useCallback(async (productId, page = 1, limit = 20) => {\n    setIsLoadingVariants(true);\n    setError(null);\n\n    try {\n      // Simulate API call delay\n      await new Promise(resolve => setTimeout(resolve, 800));\n\n      // In production, this would be an actual API call:\n      // const authHeaders = await getAuthHeaders();\n      // const response = await fetch(`/api/v1/products/${productId}/variants?page=${page}&limit=${limit}`, {\n      //   headers: {\n      //     'Content-Type': 'application/json',\n      //     ...authHeaders\n      //   }\n      // });\n      // const data = await response.json();\n\n      // For now, generate mock data\n      const mockVariants = generateMockVariants(limit);\n      const totalCount = 135; // Mock total count\n\n      setVariants(mockVariants);\n      setTotalVariants(totalCount);\n\n      // Mock product data\n      setProductData({\n        id: productId,\n        title: '3D Gold Herz Silikon Soft Phone Fall Für iPhone 15 14 13 12 11 16 Pro Max Xs Xr 7 8 plus Se Stoß Feste Süßigkeiten Matte Abdeckung',\n        enriched: true,\n        enrichmentSource: 'comprehensive_rapidapi',\n        locale: { language: 'de', currency: 'EUR', region: 'DE' }\n      });\n\n    } catch (err) {\n      setError(err.message || 'Failed to fetch variants');\n      console.error('Error fetching variants:', err);\n    } finally {\n      setIsLoadingVariants(false);\n    }\n  }, []);\n\n  const updateVariant = useCallback(async (variantId, updates) => {\n    try {\n      // In production, this would be an actual API call:\n      // const authHeaders = await getAuthHeaders();\n      // const response = await fetch(`/api/v1/variants/${variantId}`, {\n      //   method: 'PATCH',\n      //   headers: {\n      //     'Content-Type': 'application/json',\n      //     ...authHeaders\n      //   },\n      //   body: JSON.stringify(updates)\n      // });\n\n      // Mock update\n      setVariants(prev => \n        prev.map(variant => \n          variant.id === variantId \n            ? { ...variant, ...updates }\n            : variant\n        )\n      );\n\n      return { success: true };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to update variant');\n    }\n  }, []);\n\n  const deleteVariant = useCallback(async (variantId) => {\n    try {\n      // In production, this would be an actual API call:\n      // const response = await fetch(`/api/v1/variants/${variantId}`, {\n      //   method: 'DELETE'\n      // });\n\n      // Mock deletion\n      setVariants(prev => prev.filter(variant => variant.id !== variantId));\n      setTotalVariants(prev => prev - 1);\n\n      return { success: true };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to delete variant');\n    }\n  }, []);\n\n  const bulkUpdateVariants = useCallback(async (variantIds, updates) => {\n    try {\n      // In production, this would be an actual API call:\n      // const response = await fetch('/api/v1/variants/bulk-update', {\n      //   method: 'PATCH',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({ variantIds, updates })\n      // });\n\n      // Mock bulk update\n      setVariants(prev => \n        prev.map(variant => \n          variantIds.includes(variant.id)\n            ? { ...variant, ...updates }\n            : variant\n        )\n      );\n\n      return { success: true, updatedCount: variantIds.length };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to bulk update variants');\n    }\n  }, []);\n\n  const saveAndImportProduct = useCallback(async (productId, variantsToImport) => {\n    try {\n      // In production, this would call our comprehensive enrichment system:\n      // const response = await fetch('/api/v1/products/save-and-import', {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify({\n      //     productId,\n      //     variants: variantsToImport,\n      //     locale: { language: 'de', currency: 'EUR', region: 'DE' }\n      //   })\n      // });\n\n      // Mock save and import\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      return { \n        success: true, \n        importedCount: variantsToImport.length,\n        message: 'Product and variants imported successfully'\n      };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to save and import product');\n    }\n  }, []);\n\n  const addVariant = useCallback(async (productId, variantData) => {\n    try {\n      // In production, this would be an actual API call:\n      // const response = await fetch(`/api/v1/products/${productId}/variants`, {\n      //   method: 'POST',\n      //   headers: { 'Content-Type': 'application/json' },\n      //   body: JSON.stringify(variantData)\n      // });\n\n      // Mock add variant\n      const newVariant = {\n        id: `variant_${Date.now()}_new`,\n        ...variantData,\n        status: 'IN_STOCK',\n        buyId: `new_${Date.now()}`,\n        price: variantData.price || 8.47,\n        currency: 'EUR'\n      };\n\n      setVariants(prev => [...prev, newVariant]);\n      setTotalVariants(prev => prev + 1);\n\n      return { success: true, variant: newVariant };\n    } catch (err) {\n      throw new Error(err.message || 'Failed to add variant');\n    }\n  }, []);\n\n  return {\n    productData,\n    variants,\n    totalVariants,\n    isLoadingVariants,\n    error,\n    fetchVariants,\n    updateVariant,\n    deleteVariant,\n    bulkUpdateVariants,\n    saveAndImportProduct,\n    addVariant\n  };\n};\n\nexport { useVariants };\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAE7C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB;EACA,MAAMC,cAAc,GAAGA,CAAA,MAAO;IAAE,eAAe,EAAE;EAAoB,CAAC,CAAC;EACvE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACS,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAACW,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMe,oBAAoB,GAAGA,CAACC,KAAK,GAAG,EAAE,KAAK;IAC3C,MAAMC,MAAM,GAAG,CACb;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAU,CAAC,EACpC;MAAED,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAO,CAAC,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAC,EAChC;MAAED,IAAI,EAAE,KAAK;MAAEC,MAAM,EAAE;IAAM,CAAC,EAC9B;MAAED,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAO,CAAC,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAO,CAAC,CACjC;IAED,MAAMC,SAAS,GAAG,CAChB,gBAAgB,EAChB,mBAAmB,EACnB,eAAe,EACf,eAAe,EACf,eAAe,CAChB;IAED,MAAMC,QAAQ,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,CAAC;IACxD,MAAMC,YAAY,GAAG,EAAE;IAEvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,KAAK,EAAEO,CAAC,EAAE,EAAE;MAC9B,MAAMC,KAAK,GAAGP,MAAM,CAACM,CAAC,GAAGN,MAAM,CAACQ,MAAM,CAAC;MACvC,MAAMC,QAAQ,GAAGN,SAAS,CAACG,CAAC,GAAGH,SAAS,CAACK,MAAM,CAAC;MAChD,MAAME,MAAM,GAAGN,QAAQ,CAACE,CAAC,GAAGF,QAAQ,CAACI,MAAM,CAAC;MAE5CH,YAAY,CAACM,IAAI,CAAC;QAChBC,EAAE,EAAE,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIR,CAAC,EAAE;QAChCL,IAAI,EAAE,GAAGM,KAAK,CAACL,MAAM,IAAIO,QAAQ,EAAE;QACnCF,KAAK,EAAEA,KAAK,CAACL,MAAM;QACnBO,QAAQ,EAAEA,QAAQ;QAClBM,IAAI,EAAE,IAAI;QACVC,KAAK,EAAEP,QAAQ;QACfQ,KAAK,EAAE,IAAI,GAAIX,CAAC,GAAG,GAAI;QACvBY,QAAQ,EAAE,KAAK;QACfR,MAAM,EAAEA,MAAM;QACdS,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE;QAC3CC,KAAK,EAAE,wBAAwBjB,CAAC,EAAE;QAClCkB,GAAG,EAAE,MAAMjB,KAAK,CAACN,IAAI,CAACwB,WAAW,CAAC,CAAC,IAAInB,CAAC,CAACoB,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;QACtEC,KAAK,EAAE,qCAAqC,aAAa,GAAGtB,CAAC,uBAAuB;QACpFuB,MAAM,EAAE,CACN,qCAAqC,aAAa,GAAGvB,CAAC,QAAQ,EAC9D,qCAAqC,aAAa,GAAGA,CAAC,QAAQ,CAC/D;QACDwB,OAAO,EAAE,CACP;UACE7B,IAAI,EAAE,OAAO;UACb8B,KAAK,EAAExB,KAAK,CAACL,MAAM;UACnB8B,aAAa,EAAEzB,KAAK,CAACN;QACvB,CAAC,EACD;UACEA,IAAI,EAAE,UAAU;UAChB8B,KAAK,EAAEtB,QAAQ;UACfuB,aAAa,EAAEvB;QACjB,CAAC;MAEL,CAAC,CAAC;IACJ;IAEA,OAAOJ,YAAY;EACrB,CAAC;EAED,MAAM4B,aAAa,GAAGjD,WAAW,CAAC,OAAOkD,SAAS,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,KAAK;IAC3EzC,oBAAoB,CAAC,IAAI,CAAC;IAC1BE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAM,IAAIwC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;MAEtD;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAMjC,YAAY,GAAGP,oBAAoB,CAACsC,KAAK,CAAC;MAChD,MAAMI,UAAU,GAAG,GAAG,CAAC,CAAC;;MAExBjD,WAAW,CAACc,YAAY,CAAC;MACzBZ,gBAAgB,CAAC+C,UAAU,CAAC;;MAE5B;MACAnD,cAAc,CAAC;QACbuB,EAAE,EAAEsB,SAAS;QACbO,KAAK,EAAE,oIAAoI;QAC3IC,QAAQ,EAAE,IAAI;QACdC,gBAAgB,EAAE,wBAAwB;QAC1CC,MAAM,EAAE;UAAEC,QAAQ,EAAE,IAAI;UAAE3B,QAAQ,EAAE,KAAK;UAAE4B,MAAM,EAAE;QAAK;MAC1D,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZlD,QAAQ,CAACkD,GAAG,CAACC,OAAO,IAAI,0BAA0B,CAAC;MACnDC,OAAO,CAACrD,KAAK,CAAC,0BAA0B,EAAEmD,GAAG,CAAC;IAChD,CAAC,SAAS;MACRpD,oBAAoB,CAAC,KAAK,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuD,aAAa,GAAGlE,WAAW,CAAC,OAAOmE,SAAS,EAAEC,OAAO,KAAK;IAC9D,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA7D,WAAW,CAAC8D,IAAI,IACdA,IAAI,CAACC,GAAG,CAACC,OAAO,IACdA,OAAO,CAAC3C,EAAE,KAAKuC,SAAS,GACpB;QAAE,GAAGI,OAAO;QAAE,GAAGH;MAAQ,CAAC,GAC1BG,OACN,CACF,CAAC;MAED,OAAO;QAAEC,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZ,MAAM,IAAIU,KAAK,CAACV,GAAG,CAACC,OAAO,IAAI,0BAA0B,CAAC;IAC5D;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,aAAa,GAAG1E,WAAW,CAAC,MAAOmE,SAAS,IAAK;IACrD,IAAI;MACF;MACA;MACA;MACA;;MAEA;MACA5D,WAAW,CAAC8D,IAAI,IAAIA,IAAI,CAACM,MAAM,CAACJ,OAAO,IAAIA,OAAO,CAAC3C,EAAE,KAAKuC,SAAS,CAAC,CAAC;MACrE1D,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAElC,OAAO;QAAEG,OAAO,EAAE;MAAK,CAAC;IAC1B,CAAC,CAAC,OAAOT,GAAG,EAAE;MACZ,MAAM,IAAIU,KAAK,CAACV,GAAG,CAACC,OAAO,IAAI,0BAA0B,CAAC;IAC5D;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMY,kBAAkB,GAAG5E,WAAW,CAAC,OAAO6E,UAAU,EAAET,OAAO,KAAK;IACpE,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA7D,WAAW,CAAC8D,IAAI,IACdA,IAAI,CAACC,GAAG,CAACC,OAAO,IACdM,UAAU,CAACC,QAAQ,CAACP,OAAO,CAAC3C,EAAE,CAAC,GAC3B;QAAE,GAAG2C,OAAO;QAAE,GAAGH;MAAQ,CAAC,GAC1BG,OACN,CACF,CAAC;MAED,OAAO;QAAEC,OAAO,EAAE,IAAI;QAAEO,YAAY,EAAEF,UAAU,CAACrD;MAAO,CAAC;IAC3D,CAAC,CAAC,OAAOuC,GAAG,EAAE;MACZ,MAAM,IAAIU,KAAK,CAACV,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IAClE;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgB,oBAAoB,GAAGhF,WAAW,CAAC,OAAOkD,SAAS,EAAE+B,gBAAgB,KAAK;IAC9E,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAM,IAAI5B,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,OAAO;QACLkB,OAAO,EAAE,IAAI;QACbU,aAAa,EAAED,gBAAgB,CAACzD,MAAM;QACtCwC,OAAO,EAAE;MACX,CAAC;IACH,CAAC,CAAC,OAAOD,GAAG,EAAE;MACZ,MAAM,IAAIU,KAAK,CAACV,GAAG,CAACC,OAAO,IAAI,mCAAmC,CAAC;IACrE;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,UAAU,GAAGnF,WAAW,CAAC,OAAOkD,SAAS,EAAEkC,WAAW,KAAK;IAC/D,IAAI;MACF;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA,MAAMC,UAAU,GAAG;QACjBzD,EAAE,EAAE,WAAWC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;QAC/B,GAAGsD,WAAW;QACd1D,MAAM,EAAE,UAAU;QAClBa,KAAK,EAAE,OAAOV,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC1BG,KAAK,EAAEmD,WAAW,CAACnD,KAAK,IAAI,IAAI;QAChCC,QAAQ,EAAE;MACZ,CAAC;MAED3B,WAAW,CAAC8D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEgB,UAAU,CAAC,CAAC;MAC1C5E,gBAAgB,CAAC4D,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAElC,OAAO;QAAEG,OAAO,EAAE,IAAI;QAAED,OAAO,EAAEc;MAAW,CAAC;IAC/C,CAAC,CAAC,OAAOtB,GAAG,EAAE;MACZ,MAAM,IAAIU,KAAK,CAACV,GAAG,CAACC,OAAO,IAAI,uBAAuB,CAAC;IACzD;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL5D,WAAW;IACXE,QAAQ;IACRE,aAAa;IACbE,iBAAiB;IACjBE,KAAK;IACLqC,aAAa;IACbiB,aAAa;IACbQ,aAAa;IACbE,kBAAkB;IAClBI,oBAAoB;IACpBG;EACF,CAAC;AACH,CAAC;AAACjF,EAAA,CA1PID,WAAW;AA4PjB,SAASA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}