{"version": 3, "file": "index.js", "names": ["tinyColor", "throwError", "isNodePattern", "applyKernel", "im", "kernel", "x", "y", "value", "size", "length", "kx", "ky", "idx", "getPixelIndex", "bitmap", "data", "isDef", "v", "greyscale", "cb", "scanQuiet", "width", "height", "grey", "parseInt", "call", "mix", "clr", "clr2", "p", "r", "g", "b", "colorFn", "actions", "Array", "isArray", "map", "action", "apply", "params", "toRgb", "colorModifier", "i", "amount", "constructor", "limit255", "for<PERSON>ach", "ColorActionName", "Object", "freeze", "LIGHTEN", "BRIGHTEN", "DARKEN", "DESATURATE", "SATURATE", "GREYSCALE", "SPIN", "HUE", "MIX", "TINT", "SHADE", "XOR", "RED", "GREEN", "BLUE", "brightness", "val", "contrast", "factor", "adjust", "Math", "floor", "posterize", "n", "grayscale", "opacity", "f", "sepia", "red", "green", "blue", "fade", "convolution", "edgeHandling", "EDGE_EXTEND", "newData", "<PERSON><PERSON><PERSON>", "from", "kRows", "kCols", "rowEnd", "colEnd", "rowIni", "colIni", "weight", "rSum", "gSum", "bSum", "ri", "gi", "bi", "xi", "yi", "idxi", "row", "col", "opaque", "pixelate", "w", "h", "source", "clone<PERSON>uiet", "xx", "yx", "convolute", "color", "colour"], "sources": ["../src/index.js"], "sourcesContent": ["import tinyColor from \"tinycolor2\";\nimport { throwError, isNodePattern } from \"@jimp/utils\";\n\nfunction applyKernel(im, kernel, x, y) {\n  const value = [0, 0, 0];\n  const size = (kernel.length - 1) / 2;\n\n  for (let kx = 0; kx < kernel.length; kx += 1) {\n    for (let ky = 0; ky < kernel[kx].length; ky += 1) {\n      const idx = im.getPixelIndex(x + kx - size, y + ky - size);\n\n      value[0] += im.bitmap.data[idx] * kernel[kx][ky];\n      value[1] += im.bitmap.data[idx + 1] * kernel[kx][ky];\n      value[2] += im.bitmap.data[idx + 2] * kernel[kx][ky];\n    }\n  }\n\n  return value;\n}\n\nconst isDef = (v) => typeof v !== \"undefined\" && v !== null;\n\nfunction greyscale(cb) {\n  this.scanQuiet(\n    0,\n    0,\n    this.bitmap.width,\n    this.bitmap.height,\n    function (x, y, idx) {\n      const grey = parseInt(\n        0.2126 * this.bitmap.data[idx] +\n          0.7152 * this.bitmap.data[idx + 1] +\n          0.0722 * this.bitmap.data[idx + 2],\n        10\n      );\n\n      this.bitmap.data[idx] = grey;\n      this.bitmap.data[idx + 1] = grey;\n      this.bitmap.data[idx + 2] = grey;\n    }\n  );\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nfunction mix(clr, clr2, p = 50) {\n  return {\n    r: (clr2.r - clr.r) * (p / 100) + clr.r,\n    g: (clr2.g - clr.g) * (p / 100) + clr.g,\n    b: (clr2.b - clr.b) * (p / 100) + clr.b,\n  };\n}\n\nfunction colorFn(actions, cb) {\n  if (!actions || !Array.isArray(actions)) {\n    return throwError.call(this, \"actions must be an array\", cb);\n  }\n\n  actions = actions.map((action) => {\n    if (action.apply === \"xor\" || action.apply === \"mix\") {\n      action.params[0] = tinyColor(action.params[0]).toRgb();\n    }\n\n    return action;\n  });\n\n  this.scanQuiet(0, 0, this.bitmap.width, this.bitmap.height, (x, y, idx) => {\n    let clr = {\n      r: this.bitmap.data[idx],\n      g: this.bitmap.data[idx + 1],\n      b: this.bitmap.data[idx + 2],\n    };\n\n    const colorModifier = (i, amount) =>\n      this.constructor.limit255(clr[i] + amount);\n\n    actions.forEach((action) => {\n      if (action.apply === \"mix\") {\n        clr = mix(clr, action.params[0], action.params[1]);\n      } else if (action.apply === \"tint\") {\n        clr = mix(clr, { r: 255, g: 255, b: 255 }, action.params[0]);\n      } else if (action.apply === \"shade\") {\n        clr = mix(clr, { r: 0, g: 0, b: 0 }, action.params[0]);\n      } else if (action.apply === \"xor\") {\n        clr = {\n          r: clr.r ^ action.params[0].r,\n          g: clr.g ^ action.params[0].g,\n          b: clr.b ^ action.params[0].b,\n        };\n      } else if (action.apply === \"red\") {\n        clr.r = colorModifier(\"r\", action.params[0]);\n      } else if (action.apply === \"green\") {\n        clr.g = colorModifier(\"g\", action.params[0]);\n      } else if (action.apply === \"blue\") {\n        clr.b = colorModifier(\"b\", action.params[0]);\n      } else {\n        if (action.apply === \"hue\") {\n          action.apply = \"spin\";\n        }\n\n        clr = tinyColor(clr);\n\n        if (!clr[action.apply]) {\n          return throwError.call(\n            this,\n            \"action \" + action.apply + \" not supported\",\n            cb\n          );\n        }\n\n        clr = clr[action.apply](...action.params).toRgb();\n      }\n    });\n\n    this.bitmap.data[idx] = clr.r;\n    this.bitmap.data[idx + 1] = clr.g;\n    this.bitmap.data[idx + 2] = clr.b;\n  });\n\n  if (isNodePattern(cb)) {\n    cb.call(this, null, this);\n  }\n\n  return this;\n}\n\nexport const ColorActionName = Object.freeze({\n  LIGHTEN: \"lighten\",\n  BRIGHTEN: \"brighten\",\n  DARKEN: \"darken\",\n  DESATURATE: \"desaturate\",\n  SATURATE: \"saturate\",\n  GREYSCALE: \"greyscale\",\n  SPIN: \"spin\",\n  HUE: \"hue\",\n  MIX: \"mix\",\n  TINT: \"tint\",\n  SHADE: \"shade\",\n  XOR: \"xor\",\n  RED: \"red\",\n  GREEN: \"green\",\n  BLUE: \"blue\",\n});\n\nexport default () => ({\n  /**\n   * Adjusts the brightness of the image\n   * @param {number} val the amount to adjust the brightness, a number between -1 and +1\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  brightness(val, cb) {\n    if (typeof val !== \"number\") {\n      return throwError.call(this, \"val must be numbers\", cb);\n    }\n\n    if (val < -1 || val > +1) {\n      return throwError.call(\n        this,\n        \"val must be a number between -1 and +1\",\n        cb\n      );\n    }\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        if (val < 0.0) {\n          this.bitmap.data[idx] *= 1 + val;\n          this.bitmap.data[idx + 1] *= 1 + val;\n          this.bitmap.data[idx + 2] *= 1 + val;\n        } else {\n          this.bitmap.data[idx] += (255 - this.bitmap.data[idx]) * val;\n          this.bitmap.data[idx + 1] += (255 - this.bitmap.data[idx + 1]) * val;\n          this.bitmap.data[idx + 2] += (255 - this.bitmap.data[idx + 2]) * val;\n        }\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Adjusts the contrast of the image\n   * @param {number} val the amount to adjust the contrast, a number between -1 and +1\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  contrast(val, cb) {\n    if (typeof val !== \"number\") {\n      return throwError.call(this, \"val must be numbers\", cb);\n    }\n\n    if (val < -1 || val > +1) {\n      return throwError.call(\n        this,\n        \"val must be a number between -1 and +1\",\n        cb\n      );\n    }\n\n    const factor = (val + 1) / (1 - val);\n\n    function adjust(value) {\n      value = Math.floor(factor * (value - 127) + 127);\n\n      return value < 0 ? 0 : value > 255 ? 255 : value;\n    }\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx] = adjust(this.bitmap.data[idx]);\n        this.bitmap.data[idx + 1] = adjust(this.bitmap.data[idx + 1]);\n        this.bitmap.data[idx + 2] = adjust(this.bitmap.data[idx + 2]);\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Apply a posterize effect\n   * @param {number} n the amount to adjust the contrast, minimum threshold is two\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  posterize(n, cb) {\n    if (typeof n !== \"number\") {\n      return throwError.call(this, \"n must be numbers\", cb);\n    }\n\n    if (n < 2) {\n      n = 2;\n    } // minimum of 2 levels\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx] =\n          (Math.floor((this.bitmap.data[idx] / 255) * (n - 1)) / (n - 1)) * 255;\n        this.bitmap.data[idx + 1] =\n          (Math.floor((this.bitmap.data[idx + 1] / 255) * (n - 1)) / (n - 1)) *\n          255;\n        this.bitmap.data[idx + 2] =\n          (Math.floor((this.bitmap.data[idx + 2] / 255) * (n - 1)) / (n - 1)) *\n          255;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Removes colour from the image using ITU Rec 709 luminance values\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  greyscale,\n\n  // Alias of greyscale for our American friends\n  grayscale: greyscale,\n\n  /**\n   * Multiplies the opacity of each pixel by a factor between 0 and 1\n   * @param {number} f A number, the factor by which to multiply the opacity of each pixel\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  opacity(f, cb) {\n    if (typeof f !== \"number\")\n      return throwError.call(this, \"f must be a number\", cb);\n    if (f < 0 || f > 1)\n      return throwError.call(this, \"f must be a number from 0 to 1\", cb);\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        const v = this.bitmap.data[idx + 3] * f;\n        this.bitmap.data[idx + 3] = v;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Applies a sepia tone to the image\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  sepia(cb) {\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        let red = this.bitmap.data[idx];\n        let green = this.bitmap.data[idx + 1];\n        let blue = this.bitmap.data[idx + 2];\n\n        red = red * 0.393 + green * 0.769 + blue * 0.189;\n        green = red * 0.349 + green * 0.686 + blue * 0.168;\n        blue = red * 0.272 + green * 0.534 + blue * 0.131;\n\n        this.bitmap.data[idx] = red < 255 ? red : 255;\n        this.bitmap.data[idx + 1] = green < 255 ? green : 255;\n        this.bitmap.data[idx + 2] = blue < 255 ? blue : 255;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Fades each pixel by a factor between 0 and 1\n   * @param {number} f A number from 0 to 1. 0 will haven no effect. 1 will turn the image completely transparent.\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  fade(f, cb) {\n    if (typeof f !== \"number\") {\n      return throwError.call(this, \"f must be a number\", cb);\n    }\n\n    if (f < 0 || f > 1) {\n      return throwError.call(this, \"f must be a number from 0 to 1\", cb);\n    }\n\n    // this method is an alternative to opacity (which may be deprecated)\n    this.opacity(1 - f);\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Adds each element of the image to its local neighbors, weighted by the kernel\n   * @param {array} kernel a matrix to weight the neighbors sum\n   * @param {number} edgeHandling (optional) define how to sum pixels from outside the border\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  convolution(kernel, edgeHandling, cb) {\n    if (typeof edgeHandling === \"function\" && typeof cb === \"undefined\") {\n      cb = edgeHandling;\n      edgeHandling = null;\n    }\n\n    if (!edgeHandling) {\n      edgeHandling = this.constructor.EDGE_EXTEND;\n    }\n\n    const newData = Buffer.from(this.bitmap.data);\n    const kRows = kernel.length;\n    const kCols = kernel[0].length;\n    const rowEnd = Math.floor(kRows / 2);\n    const colEnd = Math.floor(kCols / 2);\n    const rowIni = -rowEnd;\n    const colIni = -colEnd;\n\n    let weight;\n    let rSum;\n    let gSum;\n    let bSum;\n    let ri;\n    let gi;\n    let bi;\n    let xi;\n    let yi;\n    let idxi;\n\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        bSum = 0;\n        gSum = 0;\n        rSum = 0;\n\n        for (let row = rowIni; row <= rowEnd; row++) {\n          for (let col = colIni; col <= colEnd; col++) {\n            xi = x + col;\n            yi = y + row;\n            weight = kernel[row + rowEnd][col + colEnd];\n            idxi = this.getPixelIndex(xi, yi, edgeHandling);\n\n            if (idxi === -1) {\n              bi = 0;\n              gi = 0;\n              ri = 0;\n            } else {\n              ri = this.bitmap.data[idxi + 0];\n              gi = this.bitmap.data[idxi + 1];\n              bi = this.bitmap.data[idxi + 2];\n            }\n\n            rSum += weight * ri;\n            gSum += weight * gi;\n            bSum += weight * bi;\n          }\n        }\n\n        if (rSum < 0) {\n          rSum = 0;\n        }\n\n        if (gSum < 0) {\n          gSum = 0;\n        }\n\n        if (bSum < 0) {\n          bSum = 0;\n        }\n\n        if (rSum > 255) {\n          rSum = 255;\n        }\n\n        if (gSum > 255) {\n          gSum = 255;\n        }\n\n        if (bSum > 255) {\n          bSum = 255;\n        }\n\n        newData[idx + 0] = rSum;\n        newData[idx + 1] = gSum;\n        newData[idx + 2] = bSum;\n      }\n    );\n\n    this.bitmap.data = newData;\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Set the alpha channel on every pixel to fully opaque\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  opaque(cb) {\n    this.scanQuiet(\n      0,\n      0,\n      this.bitmap.width,\n      this.bitmap.height,\n      function (x, y, idx) {\n        this.bitmap.data[idx + 3] = 255;\n      }\n    );\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Pixelates the image or a region\n   * @param {number} size the size of the pixels\n   * @param {number} x (optional) the x position of the region to pixelate\n   * @param {number} y (optional) the y position of the region to pixelate\n   * @param {number} w (optional) the width of the region to pixelate\n   * @param {number} h (optional) the height of the region to pixelate\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  pixelate(size, x, y, w, h, cb) {\n    if (typeof x === \"function\") {\n      cb = x;\n      h = null;\n      w = null;\n      y = null;\n      x = null;\n    } else {\n      if (typeof size !== \"number\") {\n        return throwError.call(this, \"size must be a number\", cb);\n      }\n\n      if (isDef(x) && typeof x !== \"number\") {\n        return throwError.call(this, \"x must be a number\", cb);\n      }\n\n      if (isDef(y) && typeof y !== \"number\") {\n        return throwError.call(this, \"y must be a number\", cb);\n      }\n\n      if (isDef(w) && typeof w !== \"number\") {\n        return throwError.call(this, \"w must be a number\", cb);\n      }\n\n      if (isDef(h) && typeof h !== \"number\") {\n        return throwError.call(this, \"h must be a number\", cb);\n      }\n    }\n\n    const kernel = [\n      [1 / 16, 2 / 16, 1 / 16],\n      [2 / 16, 4 / 16, 2 / 16],\n      [1 / 16, 2 / 16, 1 / 16],\n    ];\n\n    x = x || 0;\n    y = y || 0;\n    w = isDef(w) ? w : this.bitmap.width - x;\n    h = isDef(h) ? h : this.bitmap.height - y;\n\n    const source = this.cloneQuiet();\n\n    this.scanQuiet(x, y, w, h, function (xx, yx, idx) {\n      xx = size * Math.floor(xx / size);\n      yx = size * Math.floor(yx / size);\n\n      const value = applyKernel(source, kernel, xx, yx);\n\n      this.bitmap.data[idx] = value[0];\n      this.bitmap.data[idx + 1] = value[1];\n      this.bitmap.data[idx + 2] = value[2];\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Applies a convolution kernel to the image or a region\n   * @param {array} kernel the convolution kernel\n   * @param {number} x (optional) the x position of the region to apply convolution to\n   * @param {number} y (optional) the y position of the region to apply convolution to\n   * @param {number} w (optional) the width of the region to apply convolution to\n   * @param {number} h (optional) the height of the region to apply convolution to\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp} this for chaining of methods\n   */\n  convolute(kernel, x, y, w, h, cb) {\n    if (!Array.isArray(kernel))\n      return throwError.call(this, \"the kernel must be an array\", cb);\n\n    if (typeof x === \"function\") {\n      cb = x;\n      x = null;\n      y = null;\n      w = null;\n      h = null;\n    } else {\n      if (isDef(x) && typeof x !== \"number\") {\n        return throwError.call(this, \"x must be a number\", cb);\n      }\n\n      if (isDef(y) && typeof y !== \"number\") {\n        return throwError.call(this, \"y must be a number\", cb);\n      }\n\n      if (isDef(w) && typeof w !== \"number\") {\n        return throwError.call(this, \"w must be a number\", cb);\n      }\n\n      if (isDef(h) && typeof h !== \"number\") {\n        return throwError.call(this, \"h must be a number\", cb);\n      }\n    }\n\n    x = isDef(x) ? x : 0;\n    y = isDef(y) ? y : 0;\n    w = isDef(w) ? w : this.bitmap.width - x;\n    h = isDef(h) ? h : this.bitmap.height - y;\n\n    const source = this.cloneQuiet();\n\n    this.scanQuiet(x, y, w, h, function (xx, yx, idx) {\n      const value = applyKernel(source, kernel, xx, yx);\n\n      this.bitmap.data[idx] = this.constructor.limit255(value[0]);\n      this.bitmap.data[idx + 1] = this.constructor.limit255(value[1]);\n      this.bitmap.data[idx + 2] = this.constructor.limit255(value[2]);\n    });\n\n    if (isNodePattern(cb)) {\n      cb.call(this, null, this);\n    }\n\n    return this;\n  },\n\n  /**\n   * Apply multiple color modification rules\n   * @param {array} actions list of color modification rules, in following format: { apply: '<rule-name>', params: [ <rule-parameters> ]  }\n   * @param {function(Error, Jimp)} cb (optional) a callback for when complete\n   * @returns {Jimp }this for chaining of methods\n   */\n  color: colorFn,\n  colour: colorFn,\n});\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,EAAEC,aAAa,QAAQ,aAAa;AAEvD,SAASC,WAAW,CAACC,EAAE,EAAEC,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAE;EACrC,MAAMC,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACvB,MAAMC,IAAI,GAAG,CAACJ,MAAM,CAACK,MAAM,GAAG,CAAC,IAAI,CAAC;EAEpC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGN,MAAM,CAACK,MAAM,EAAEC,EAAE,IAAI,CAAC,EAAE;IAC5C,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGP,MAAM,CAACM,EAAE,CAAC,CAACD,MAAM,EAAEE,EAAE,IAAI,CAAC,EAAE;MAChD,MAAMC,GAAG,GAAGT,EAAE,CAACU,aAAa,CAACR,CAAC,GAAGK,EAAE,GAAGF,IAAI,EAAEF,CAAC,GAAGK,EAAE,GAAGH,IAAI,CAAC;MAE1DD,KAAK,CAAC,CAAC,CAAC,IAAIJ,EAAE,CAACW,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGR,MAAM,CAACM,EAAE,CAAC,CAACC,EAAE,CAAC;MAChDJ,KAAK,CAAC,CAAC,CAAC,IAAIJ,EAAE,CAACW,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGR,MAAM,CAACM,EAAE,CAAC,CAACC,EAAE,CAAC;MACpDJ,KAAK,CAAC,CAAC,CAAC,IAAIJ,EAAE,CAACW,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGR,MAAM,CAACM,EAAE,CAAC,CAACC,EAAE,CAAC;IACtD;EACF;EAEA,OAAOJ,KAAK;AACd;AAEA,MAAMS,KAAK,GAAIC,CAAC,IAAK,OAAOA,CAAC,KAAK,WAAW,IAAIA,CAAC,KAAK,IAAI;AAE3D,SAASC,SAAS,CAACC,EAAE,EAAE;EACrB,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;IACnB,MAAMW,IAAI,GAAGC,QAAQ,CACnB,MAAM,GAAG,IAAI,CAACV,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAC5B,MAAM,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAClC,MAAM,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,EACpC,EAAE,CACH;IAED,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGW,IAAI;IAC5B,IAAI,CAACT,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGW,IAAI;IAChC,IAAI,CAACT,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGW,IAAI;EAClC,CAAC,CACF;EAED,IAAItB,aAAa,CAACkB,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;AAEA,SAASC,GAAG,CAACC,GAAG,EAAEC,IAAI,EAAU;EAAA,IAARC,CAAC,uEAAG,EAAE;EAC5B,OAAO;IACLC,CAAC,EAAE,CAACF,IAAI,CAACE,CAAC,GAAGH,GAAG,CAACG,CAAC,KAAKD,CAAC,GAAG,GAAG,CAAC,GAAGF,GAAG,CAACG,CAAC;IACvCC,CAAC,EAAE,CAACH,IAAI,CAACG,CAAC,GAAGJ,GAAG,CAACI,CAAC,KAAKF,CAAC,GAAG,GAAG,CAAC,GAAGF,GAAG,CAACI,CAAC;IACvCC,CAAC,EAAE,CAACJ,IAAI,CAACI,CAAC,GAAGL,GAAG,CAACK,CAAC,KAAKH,CAAC,GAAG,GAAG,CAAC,GAAGF,GAAG,CAACK;EACxC,CAAC;AACH;AAEA,SAASC,OAAO,CAACC,OAAO,EAAEf,EAAE,EAAE;EAC5B,IAAI,CAACe,OAAO,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;IACvC,OAAOlC,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,0BAA0B,EAAEN,EAAE,CAAC;EAC9D;EAEAe,OAAO,GAAGA,OAAO,CAACG,GAAG,CAAEC,MAAM,IAAK;IAChC,IAAIA,MAAM,CAACC,KAAK,KAAK,KAAK,IAAID,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;MACpDD,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,GAAGzC,SAAS,CAACuC,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,EAAE;IACxD;IAEA,OAAOH,MAAM;EACf,CAAC,CAAC;EAEF,IAAI,CAAClB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACN,MAAM,CAACO,KAAK,EAAE,IAAI,CAACP,MAAM,CAACQ,MAAM,EAAE,CAACjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,KAAK;IACzE,IAAIe,GAAG,GAAG;MACRG,CAAC,EAAE,IAAI,CAAChB,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC;MACxBmB,CAAC,EAAE,IAAI,CAACjB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC;MAC5BoB,CAAC,EAAE,IAAI,CAAClB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC;IAC7B,CAAC;IAED,MAAM8B,aAAa,GAAG,CAACC,CAAC,EAAEC,MAAM,KAC9B,IAAI,CAACC,WAAW,CAACC,QAAQ,CAACnB,GAAG,CAACgB,CAAC,CAAC,GAAGC,MAAM,CAAC;IAE5CV,OAAO,CAACa,OAAO,CAAET,MAAM,IAAK;MAC1B,IAAIA,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;QAC1BZ,GAAG,GAAGD,GAAG,CAACC,GAAG,EAAEW,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,EAAEF,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MACpD,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,MAAM,EAAE;QAClCZ,GAAG,GAAGD,GAAG,CAACC,GAAG,EAAE;UAAEG,CAAC,EAAE,GAAG;UAAEC,CAAC,EAAE,GAAG;UAAEC,CAAC,EAAE;QAAI,CAAC,EAAEM,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9D,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,OAAO,EAAE;QACnCZ,GAAG,GAAGD,GAAG,CAACC,GAAG,EAAE;UAAEG,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAC,EAAEM,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MACxD,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;QACjCZ,GAAG,GAAG;UACJG,CAAC,EAAEH,GAAG,CAACG,CAAC,GAAGQ,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAACV,CAAC;UAC7BC,CAAC,EAAEJ,GAAG,CAACI,CAAC,GAAGO,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAACT,CAAC;UAC7BC,CAAC,EAAEL,GAAG,CAACK,CAAC,GAAGM,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAACR;QAC9B,CAAC;MACH,CAAC,MAAM,IAAIM,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;QACjCZ,GAAG,CAACG,CAAC,GAAGY,aAAa,CAAC,GAAG,EAAEJ,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,OAAO,EAAE;QACnCZ,GAAG,CAACI,CAAC,GAAGW,aAAa,CAAC,GAAG,EAAEJ,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,MAAM,IAAIF,MAAM,CAACC,KAAK,KAAK,MAAM,EAAE;QAClCZ,GAAG,CAACK,CAAC,GAAGU,aAAa,CAAC,GAAG,EAAEJ,MAAM,CAACE,MAAM,CAAC,CAAC,CAAC,CAAC;MAC9C,CAAC,MAAM;QACL,IAAIF,MAAM,CAACC,KAAK,KAAK,KAAK,EAAE;UAC1BD,MAAM,CAACC,KAAK,GAAG,MAAM;QACvB;QAEAZ,GAAG,GAAG5B,SAAS,CAAC4B,GAAG,CAAC;QAEpB,IAAI,CAACA,GAAG,CAACW,MAAM,CAACC,KAAK,CAAC,EAAE;UACtB,OAAOvC,UAAU,CAACyB,IAAI,CACpB,IAAI,EACJ,SAAS,GAAGa,MAAM,CAACC,KAAK,GAAG,gBAAgB,EAC3CpB,EAAE,CACH;QACH;QAEAQ,GAAG,GAAGA,GAAG,CAACW,MAAM,CAACC,KAAK,CAAC,CAAC,GAAGD,MAAM,CAACE,MAAM,CAAC,CAACC,KAAK,EAAE;MACnD;IACF,CAAC,CAAC;IAEF,IAAI,CAAC3B,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGe,GAAG,CAACG,CAAC;IAC7B,IAAI,CAAChB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGe,GAAG,CAACI,CAAC;IACjC,IAAI,CAACjB,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGe,GAAG,CAACK,CAAC;EACnC,CAAC,CAAC;EAEF,IAAI/B,aAAa,CAACkB,EAAE,CAAC,EAAE;IACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC3B;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,MAAMuB,eAAe,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC3CC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,IAAI,EAAE,MAAM;EACZC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE;AACR,CAAC,CAAC;AAEF,gBAAe,OAAO;EACpB;AACF;AACA;AACA;AACA;AACA;EACEC,UAAU,CAACC,GAAG,EAAEhD,EAAE,EAAE;IAClB,IAAI,OAAOgD,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOnE,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAEN,EAAE,CAAC;IACzD;IAEA,IAAIgD,GAAG,GAAG,CAAC,CAAC,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MACxB,OAAOnE,UAAU,CAACyB,IAAI,CACpB,IAAI,EACJ,wCAAwC,EACxCN,EAAE,CACH;IACH;IAEA,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAIuD,GAAG,GAAG,GAAG,EAAE;QACb,IAAI,CAACrD,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,IAAI,CAAC,GAAGuD,GAAG;QAChC,IAAI,CAACrD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGuD,GAAG;QACpC,IAAI,CAACrD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGuD,GAAG;MACtC,CAAC,MAAM;QACL,IAAI,CAACrD,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,IAAIuD,GAAG;QAC5D,IAAI,CAACrD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAIuD,GAAG;QACpE,IAAI,CAACrD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,IAAIuD,GAAG;MACtE;IACF,CAAC,CACF;IAED,IAAIlE,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE2C,QAAQ,CAACD,GAAG,EAAEhD,EAAE,EAAE;IAChB,IAAI,OAAOgD,GAAG,KAAK,QAAQ,EAAE;MAC3B,OAAOnE,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,qBAAqB,EAAEN,EAAE,CAAC;IACzD;IAEA,IAAIgD,GAAG,GAAG,CAAC,CAAC,IAAIA,GAAG,GAAG,CAAC,CAAC,EAAE;MACxB,OAAOnE,UAAU,CAACyB,IAAI,CACpB,IAAI,EACJ,wCAAwC,EACxCN,EAAE,CACH;IACH;IAEA,MAAMkD,MAAM,GAAG,CAACF,GAAG,GAAG,CAAC,KAAK,CAAC,GAAGA,GAAG,CAAC;IAEpC,SAASG,MAAM,CAAC/D,KAAK,EAAE;MACrBA,KAAK,GAAGgE,IAAI,CAACC,KAAK,CAACH,MAAM,IAAI9D,KAAK,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;MAEhD,OAAOA,KAAK,GAAG,CAAC,GAAG,CAAC,GAAGA,KAAK,GAAG,GAAG,GAAG,GAAG,GAAGA,KAAK;IAClD;IAEA,IAAI,CAACa,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAG0D,MAAM,CAAC,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,CAAC;MACrD,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG0D,MAAM,CAAC,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,CAAC;MAC7D,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG0D,MAAM,CAAC,IAAI,CAACxD,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,CAAC;IAC/D,CAAC,CACF;IAED,IAAIX,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEgD,SAAS,CAACC,CAAC,EAAEvD,EAAE,EAAE;IACf,IAAI,OAAOuD,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO1E,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,mBAAmB,EAAEN,EAAE,CAAC;IACvD;IAEA,IAAIuD,CAAC,GAAG,CAAC,EAAE;MACTA,CAAC,GAAG,CAAC;IACP,CAAC,CAAC;;IAEF,IAAI,CAACtD,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAClB2D,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC1D,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAG,GAAG,IAAK8D,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAAI,GAAG;MACvE,IAAI,CAAC5D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GACtB2D,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC1D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAK8D,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAClE,GAAG;MACL,IAAI,CAAC5D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GACtB2D,IAAI,CAACC,KAAK,CAAE,IAAI,CAAC1D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAK8D,CAAC,GAAG,CAAC,CAAC,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC,GAClE,GAAG;IACP,CAAC,CACF;IAED,IAAIzE,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACEP,SAAS;EAET;EACAyD,SAAS,EAAEzD,SAAS;EAEpB;AACF;AACA;AACA;AACA;AACA;EACE0D,OAAO,CAACC,CAAC,EAAE1D,EAAE,EAAE;IACb,IAAI,OAAO0D,CAAC,KAAK,QAAQ,EACvB,OAAO7E,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;IACxD,IAAI0D,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAChB,OAAO7E,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,gCAAgC,EAAEN,EAAE,CAAC;IAEpE,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,MAAMK,CAAC,GAAG,IAAI,CAACH,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGiE,CAAC;MACvC,IAAI,CAAC/D,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGK,CAAC;IAC/B,CAAC,CACF;IAED,IAAIhB,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACEqD,KAAK,CAAC3D,EAAE,EAAE;IACR,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAImE,GAAG,GAAG,IAAI,CAACjE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC;MAC/B,IAAIoE,KAAK,GAAG,IAAI,CAAClE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC;MACrC,IAAIqE,IAAI,GAAG,IAAI,CAACnE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC;MAEpCmE,GAAG,GAAGA,GAAG,GAAG,KAAK,GAAGC,KAAK,GAAG,KAAK,GAAGC,IAAI,GAAG,KAAK;MAChDD,KAAK,GAAGD,GAAG,GAAG,KAAK,GAAGC,KAAK,GAAG,KAAK,GAAGC,IAAI,GAAG,KAAK;MAClDA,IAAI,GAAGF,GAAG,GAAG,KAAK,GAAGC,KAAK,GAAG,KAAK,GAAGC,IAAI,GAAG,KAAK;MAEjD,IAAI,CAACnE,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGmE,GAAG,GAAG,GAAG,GAAGA,GAAG,GAAG,GAAG;MAC7C,IAAI,CAACjE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGoE,KAAK,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG;MACrD,IAAI,CAAClE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGqE,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG,GAAG;IACrD,CAAC,CACF;IAED,IAAIhF,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEyD,IAAI,CAACL,CAAC,EAAE1D,EAAE,EAAE;IACV,IAAI,OAAO0D,CAAC,KAAK,QAAQ,EAAE;MACzB,OAAO7E,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;IACxD;IAEA,IAAI0D,CAAC,GAAG,CAAC,IAAIA,CAAC,GAAG,CAAC,EAAE;MAClB,OAAO7E,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,gCAAgC,EAAEN,EAAE,CAAC;IACpE;;IAEA;IACA,IAAI,CAACyD,OAAO,CAAC,CAAC,GAAGC,CAAC,CAAC;IAEnB,IAAI5E,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACE0D,WAAW,CAAC/E,MAAM,EAAEgF,YAAY,EAAEjE,EAAE,EAAE;IACpC,IAAI,OAAOiE,YAAY,KAAK,UAAU,IAAI,OAAOjE,EAAE,KAAK,WAAW,EAAE;MACnEA,EAAE,GAAGiE,YAAY;MACjBA,YAAY,GAAG,IAAI;IACrB;IAEA,IAAI,CAACA,YAAY,EAAE;MACjBA,YAAY,GAAG,IAAI,CAACvC,WAAW,CAACwC,WAAW;IAC7C;IAEA,MAAMC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1E,MAAM,CAACC,IAAI,CAAC;IAC7C,MAAM0E,KAAK,GAAGrF,MAAM,CAACK,MAAM;IAC3B,MAAMiF,KAAK,GAAGtF,MAAM,CAAC,CAAC,CAAC,CAACK,MAAM;IAC9B,MAAMkF,MAAM,GAAGpB,IAAI,CAACC,KAAK,CAACiB,KAAK,GAAG,CAAC,CAAC;IACpC,MAAMG,MAAM,GAAGrB,IAAI,CAACC,KAAK,CAACkB,KAAK,GAAG,CAAC,CAAC;IACpC,MAAMG,MAAM,GAAG,CAACF,MAAM;IACtB,MAAMG,MAAM,GAAG,CAACF,MAAM;IAEtB,IAAIG,MAAM;IACV,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,IAAI;IACR,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,EAAE;IACN,IAAIC,IAAI;IAER,IAAI,CAACpF,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnBsF,IAAI,GAAG,CAAC;MACRD,IAAI,GAAG,CAAC;MACRD,IAAI,GAAG,CAAC;MAER,KAAK,IAAIS,GAAG,GAAGZ,MAAM,EAAEY,GAAG,IAAId,MAAM,EAAEc,GAAG,EAAE,EAAE;QAC3C,KAAK,IAAIC,GAAG,GAAGZ,MAAM,EAAEY,GAAG,IAAId,MAAM,EAAEc,GAAG,EAAE,EAAE;UAC3CJ,EAAE,GAAGjG,CAAC,GAAGqG,GAAG;UACZH,EAAE,GAAGjG,CAAC,GAAGmG,GAAG;UACZV,MAAM,GAAG3F,MAAM,CAACqG,GAAG,GAAGd,MAAM,CAAC,CAACe,GAAG,GAAGd,MAAM,CAAC;UAC3CY,IAAI,GAAG,IAAI,CAAC3F,aAAa,CAACyF,EAAE,EAAEC,EAAE,EAAEnB,YAAY,CAAC;UAE/C,IAAIoB,IAAI,KAAK,CAAC,CAAC,EAAE;YACfH,EAAE,GAAG,CAAC;YACND,EAAE,GAAG,CAAC;YACND,EAAE,GAAG,CAAC;UACR,CAAC,MAAM;YACLA,EAAE,GAAG,IAAI,CAACrF,MAAM,CAACC,IAAI,CAACyF,IAAI,GAAG,CAAC,CAAC;YAC/BJ,EAAE,GAAG,IAAI,CAACtF,MAAM,CAACC,IAAI,CAACyF,IAAI,GAAG,CAAC,CAAC;YAC/BH,EAAE,GAAG,IAAI,CAACvF,MAAM,CAACC,IAAI,CAACyF,IAAI,GAAG,CAAC,CAAC;UACjC;UAEAR,IAAI,IAAID,MAAM,GAAGI,EAAE;UACnBF,IAAI,IAAIF,MAAM,GAAGK,EAAE;UACnBF,IAAI,IAAIH,MAAM,GAAGM,EAAE;QACrB;MACF;MAEA,IAAIL,IAAI,GAAG,CAAC,EAAE;QACZA,IAAI,GAAG,CAAC;MACV;MAEA,IAAIC,IAAI,GAAG,CAAC,EAAE;QACZA,IAAI,GAAG,CAAC;MACV;MAEA,IAAIC,IAAI,GAAG,CAAC,EAAE;QACZA,IAAI,GAAG,CAAC;MACV;MAEA,IAAIF,IAAI,GAAG,GAAG,EAAE;QACdA,IAAI,GAAG,GAAG;MACZ;MAEA,IAAIC,IAAI,GAAG,GAAG,EAAE;QACdA,IAAI,GAAG,GAAG;MACZ;MAEA,IAAIC,IAAI,GAAG,GAAG,EAAE;QACdA,IAAI,GAAG,GAAG;MACZ;MAEAZ,OAAO,CAAC1E,GAAG,GAAG,CAAC,CAAC,GAAGoF,IAAI;MACvBV,OAAO,CAAC1E,GAAG,GAAG,CAAC,CAAC,GAAGqF,IAAI;MACvBX,OAAO,CAAC1E,GAAG,GAAG,CAAC,CAAC,GAAGsF,IAAI;IACzB,CAAC,CACF;IAED,IAAI,CAACpF,MAAM,CAACC,IAAI,GAAGuE,OAAO;IAE1B,IAAIrF,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;EACEkF,MAAM,CAACxF,EAAE,EAAE;IACT,IAAI,CAACC,SAAS,CACZ,CAAC,EACD,CAAC,EACD,IAAI,CAACN,MAAM,CAACO,KAAK,EACjB,IAAI,CAACP,MAAM,CAACQ,MAAM,EAClB,UAAUjB,CAAC,EAAEC,CAAC,EAAEM,GAAG,EAAE;MACnB,IAAI,CAACE,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG;IACjC,CAAC,CACF;IAED,IAAIX,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEmF,QAAQ,CAACpG,IAAI,EAAEH,CAAC,EAAEC,CAAC,EAAEuG,CAAC,EAAEC,CAAC,EAAE3F,EAAE,EAAE;IAC7B,IAAI,OAAOd,CAAC,KAAK,UAAU,EAAE;MAC3Bc,EAAE,GAAGd,CAAC;MACNyG,CAAC,GAAG,IAAI;MACRD,CAAC,GAAG,IAAI;MACRvG,CAAC,GAAG,IAAI;MACRD,CAAC,GAAG,IAAI;IACV,CAAC,MAAM;MACL,IAAI,OAAOG,IAAI,KAAK,QAAQ,EAAE;QAC5B,OAAOR,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,uBAAuB,EAAEN,EAAE,CAAC;MAC3D;MAEA,IAAIH,KAAK,CAACX,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAOL,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACV,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAON,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAAC6F,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO7G,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAAC8F,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO9G,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;IACF;IAEA,MAAMf,MAAM,GAAG,CACb,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EACxB,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,EACxB,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,CAAC,CACzB;IAEDC,CAAC,GAAGA,CAAC,IAAI,CAAC;IACVC,CAAC,GAAGA,CAAC,IAAI,CAAC;IACVuG,CAAC,GAAG7F,KAAK,CAAC6F,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAC/F,MAAM,CAACO,KAAK,GAAGhB,CAAC;IACxCyG,CAAC,GAAG9F,KAAK,CAAC8F,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAChG,MAAM,CAACQ,MAAM,GAAGhB,CAAC;IAEzC,MAAMyG,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;IAEhC,IAAI,CAAC5F,SAAS,CAACf,CAAC,EAAEC,CAAC,EAAEuG,CAAC,EAAEC,CAAC,EAAE,UAAUG,EAAE,EAAEC,EAAE,EAAEtG,GAAG,EAAE;MAChDqG,EAAE,GAAGzG,IAAI,GAAG+D,IAAI,CAACC,KAAK,CAACyC,EAAE,GAAGzG,IAAI,CAAC;MACjC0G,EAAE,GAAG1G,IAAI,GAAG+D,IAAI,CAACC,KAAK,CAAC0C,EAAE,GAAG1G,IAAI,CAAC;MAEjC,MAAMD,KAAK,GAAGL,WAAW,CAAC6G,MAAM,EAAE3G,MAAM,EAAE6G,EAAE,EAAEC,EAAE,CAAC;MAEjD,IAAI,CAACpG,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;MAChC,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;MACpC,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAGL,KAAK,CAAC,CAAC,CAAC;IACtC,CAAC,CAAC;IAEF,IAAIN,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE0F,SAAS,CAAC/G,MAAM,EAAEC,CAAC,EAAEC,CAAC,EAAEuG,CAAC,EAAEC,CAAC,EAAE3F,EAAE,EAAE;IAChC,IAAI,CAACgB,KAAK,CAACC,OAAO,CAAChC,MAAM,CAAC,EACxB,OAAOJ,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,6BAA6B,EAAEN,EAAE,CAAC;IAEjE,IAAI,OAAOd,CAAC,KAAK,UAAU,EAAE;MAC3Bc,EAAE,GAAGd,CAAC;MACNA,CAAC,GAAG,IAAI;MACRC,CAAC,GAAG,IAAI;MACRuG,CAAC,GAAG,IAAI;MACRC,CAAC,GAAG,IAAI;IACV,CAAC,MAAM;MACL,IAAI9F,KAAK,CAACX,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAOL,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAACV,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAON,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAAC6F,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO7G,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;MAEA,IAAIH,KAAK,CAAC8F,CAAC,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;QACrC,OAAO9G,UAAU,CAACyB,IAAI,CAAC,IAAI,EAAE,oBAAoB,EAAEN,EAAE,CAAC;MACxD;IACF;IAEAd,CAAC,GAAGW,KAAK,CAACX,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACpBC,CAAC,GAAGU,KAAK,CAACV,CAAC,CAAC,GAAGA,CAAC,GAAG,CAAC;IACpBuG,CAAC,GAAG7F,KAAK,CAAC6F,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAC/F,MAAM,CAACO,KAAK,GAAGhB,CAAC;IACxCyG,CAAC,GAAG9F,KAAK,CAAC8F,CAAC,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAChG,MAAM,CAACQ,MAAM,GAAGhB,CAAC;IAEzC,MAAMyG,MAAM,GAAG,IAAI,CAACC,UAAU,EAAE;IAEhC,IAAI,CAAC5F,SAAS,CAACf,CAAC,EAAEC,CAAC,EAAEuG,CAAC,EAAEC,CAAC,EAAE,UAAUG,EAAE,EAAEC,EAAE,EAAEtG,GAAG,EAAE;MAChD,MAAML,KAAK,GAAGL,WAAW,CAAC6G,MAAM,EAAE3G,MAAM,EAAE6G,EAAE,EAAEC,EAAE,CAAC;MAEjD,IAAI,CAACpG,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,GAAG,IAAI,CAACiC,WAAW,CAACC,QAAQ,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC;MAC3D,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACiC,WAAW,CAACC,QAAQ,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC;MAC/D,IAAI,CAACO,MAAM,CAACC,IAAI,CAACH,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,CAACiC,WAAW,CAACC,QAAQ,CAACvC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC,CAAC;IAEF,IAAIN,aAAa,CAACkB,EAAE,CAAC,EAAE;MACrBA,EAAE,CAACM,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC3B;IAEA,OAAO,IAAI;EACb,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACE2F,KAAK,EAAEnF,OAAO;EACdoF,MAAM,EAAEpF;AACV,CAAC,CAAC"}