{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\ebayali\\\\frontend\\\\src\\\\components\\\\UploadScreen.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { ChevronRightIcon, FunnelIcon, ArrowRightOnRectangleIcon, UserIcon } from '@heroicons/react/24/outline';\nimport VariantsTab from './VariantsTab';\nimport { useToast } from '../hooks/useToast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UploadScreen = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('drafts');\n  const [uploadCounts, setUploadCounts] = useState({\n    drafts: 1,\n    scheduled: 0,\n    recurring: 0\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const {\n    showToast\n  } = useToast();\n\n  // Mock user data\n  const user = {\n    firstName: 'Demo',\n    lastName: 'User',\n    accountType: 'pro'\n  };\n\n  // Fetch upload counts on component mount\n  useEffect(() => {\n    fetchUploadCounts();\n  }, []);\n  const fetchUploadCounts = async () => {\n    try {\n      setIsLoading(true);\n      const response = await fetch('/api/v1/uploads/counts');\n      if (response.ok) {\n        const data = await response.json();\n        setUploadCounts(data.counts || uploadCounts);\n      }\n    } catch (error) {\n      console.error('Failed to fetch upload counts:', error);\n      showToast('Failed to load upload counts', 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getTotalUploads = () => {\n    return uploadCounts.drafts + uploadCounts.scheduled + uploadCounts.recurring;\n  };\n  const handleTabChange = tabName => {\n    setActiveTab(tabName);\n  };\n  const handleFilterClick = () => {\n    // TODO: Implement filter modal\n    showToast('Filter functionality coming soon', 'info');\n  };\n  const handleLogout = () => {\n    showToast('تم تسجيل الخروج بنجاح', 'success');\n    // In a real app, this would clear auth state\n  };\n  const tabs = [{\n    id: 'drafts',\n    label: 'المسودات',\n    count: uploadCounts.drafts\n  }, {\n    id: 'scheduled',\n    label: 'المجدولة',\n    count: uploadCounts.scheduled\n  }, {\n    id: 'recurring',\n    label: 'المتكررة',\n    count: uploadCounts.recurring\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"pt-4 px-6\",\n      \"aria-label\": \"Breadcrumb\",\n      children: /*#__PURE__*/_jsxDEV(\"ol\", {\n        className: \"flex items-center space-x-2 text-sm text-gray-500\",\n        dir: \"rtl\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/dashboard\",\n            className: \"hover:text-gray-700 transition-colors\",\n            children: \"\\u0644\\u0648\\u062D\\u0629 \\u0627\\u0644\\u062A\\u062D\\u0643\\u0645\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ChevronRightIcon, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"/products\",\n            className: \"hover:text-gray-700 transition-colors\",\n            children: \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ChevronRightIcon, {\n          className: \"h-4 w-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          className: \"text-gray-800 font-medium\",\n          \"aria-current\": \"page\",\n          children: \"\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-6 mt-6\",\n      dir: \"rtl\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-semibold text-gray-900\",\n            children: \"\\u0627\\u0633\\u062A\\u064A\\u0631\\u0627\\u062F \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"mr-2 text-3xl text-gray-500\",\n            children: [\"(\", getTotalUploads(), \")\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4 space-x-reverse\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2 space-x-reverse text-sm text-gray-600\",\n            children: [/*#__PURE__*/_jsxDEV(UserIcon, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [user === null || user === void 0 ? void 0 : user.firstName, \" \", user === null || user === void 0 ? void 0 : user.lastName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), (user === null || user === void 0 ? void 0 : user.accountType) === 'trial' && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-medium\",\n              children: \"\\u062A\\u062C\\u0631\\u064A\\u0628\\u064A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"flex items-center space-x-1 space-x-reverse text-sm text-gray-500 hover:text-gray-700 transition-colors\",\n            title: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\",\n            children: [/*#__PURE__*/_jsxDEV(ArrowRightOnRectangleIcon, {\n              className: \"h-4 w-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u062A\\u0633\\u062C\\u064A\\u0644 \\u0627\\u0644\\u062E\\u0631\\u0648\\u062C\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-6 mt-8\",\n      dir: \"rtl\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-b border-gray-200 bg-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between px-6 py-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"nav\", {\n            className: \"flex space-x-8 space-x-reverse\",\n            \"aria-label\": \"Tabs\",\n            children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleTabChange(tab.id),\n              className: `\n                    py-3 px-4 text-sm font-medium border-b-2 transition-all duration-200\n                    ${activeTab === tab.id ? 'border-amber-500 text-gray-800 bg-amber-50' : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'}\n                  `,\n              \"aria-current\": activeTab === tab.id ? 'page' : undefined,\n              children: [tab.label, \" (\", tab.count, \")\"]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleFilterClick,\n            className: \"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\",\n            children: [/*#__PURE__*/_jsxDEV(FunnelIcon, {\n              className: \"h-4 w-4 ml-2 text-gray-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0641\\u0644\\u062A\\u0631\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 127,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"px-6 mt-0\",\n      children: [activeTab === 'drafts' && /*#__PURE__*/_jsxDEV(VariantsTab, {\n        onCountUpdate: newCount => setUploadCounts(prev => ({\n          ...prev,\n          drafts: newCount\n        }))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 11\n      }, this), activeTab === 'scheduled' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-12 text-center\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium\",\n            children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062C\\u062F\\u0648\\u0644\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2\",\n            children: \"\\u0633\\u062A\\u0638\\u0647\\u0631 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0627\\u0644\\u0645\\u062C\\u062F\\u0648\\u0644\\u0629 \\u0647\\u0646\\u0627 \\u0639\\u0646\\u062F \\u0625\\u0646\\u0634\\u0627\\u0626\\u0647\\u0627.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 11\n      }, this), activeTab === 'recurring' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-12 text-center\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium\",\n            children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0645\\u062A\\u0643\\u0631\\u0631\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2\",\n            children: \"\\u0633\\u062A\\u0638\\u0647\\u0631 \\u062C\\u062F\\u0627\\u0648\\u0644 \\u0627\\u0644\\u0631\\u0641\\u0639 \\u0627\\u0644\\u0645\\u062A\\u0643\\u0631\\u0631\\u0629 \\u0647\\u0646\\u0627 \\u0639\\u0646\\u062F \\u062A\\u0643\\u0648\\u064A\\u0646\\u0647\\u0627.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg p-6 flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n};\n_s(UploadScreen, \"JA/GCZFnMbBBXpd4BWBl23hEulM=\", false, function () {\n  return [useToast];\n});\n_c = UploadScreen;\nexport default UploadScreen;\nvar _c;\n$RefreshReg$(_c, \"UploadScreen\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ChevronRightIcon", "FunnelIcon", "ArrowRightOnRectangleIcon", "UserIcon", "VariantsTab", "useToast", "jsxDEV", "_jsxDEV", "UploadScreen", "_s", "activeTab", "setActiveTab", "uploadCounts", "setUploadCounts", "drafts", "scheduled", "recurring", "isLoading", "setIsLoading", "showToast", "user", "firstName", "lastName", "accountType", "fetchUploadCounts", "response", "fetch", "ok", "data", "json", "counts", "error", "console", "getTotalUploads", "handleTabChange", "tabName", "handleFilterClick", "handleLogout", "tabs", "id", "label", "count", "className", "children", "dir", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "title", "map", "tab", "undefined", "onCountUpdate", "newCount", "prev", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/ebayali/frontend/src/components/UploadScreen.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { ChevronRightIcon, FunnelIcon, ArrowRightOnRectangleIcon, UserIcon } from '@heroicons/react/24/outline';\nimport VariantsTab from './VariantsTab';\nimport { useToast } from '../hooks/useToast';\n\nconst UploadScreen = () => {\n  const [activeTab, setActiveTab] = useState('drafts');\n  const [uploadCounts, setUploadCounts] = useState({\n    drafts: 1,\n    scheduled: 0,\n    recurring: 0\n  });\n  const [isLoading, setIsLoading] = useState(false);\n  const { showToast } = useToast();\n\n  // Mock user data\n  const user = {\n    firstName: 'Demo',\n    lastName: 'User',\n    accountType: 'pro'\n  };\n\n  // Fetch upload counts on component mount\n  useEffect(() => {\n    fetchUploadCounts();\n  }, []);\n\n  const fetchUploadCounts = async () => {\n    try {\n      setIsLoading(true);\n      const response = await fetch('/api/v1/uploads/counts');\n      if (response.ok) {\n        const data = await response.json();\n        setUploadCounts(data.counts || uploadCounts);\n      }\n    } catch (error) {\n      console.error('Failed to fetch upload counts:', error);\n      showToast('Failed to load upload counts', 'error');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getTotalUploads = () => {\n    return uploadCounts.drafts + uploadCounts.scheduled + uploadCounts.recurring;\n  };\n\n  const handleTabChange = (tabName) => {\n    setActiveTab(tabName);\n  };\n\n  const handleFilterClick = () => {\n    // TODO: Implement filter modal\n    showToast('Filter functionality coming soon', 'info');\n  };\n\n  const handleLogout = () => {\n    showToast('تم تسجيل الخروج بنجاح', 'success');\n    // In a real app, this would clear auth state\n  };\n\n  const tabs = [\n    { id: 'drafts', label: 'المسودات', count: uploadCounts.drafts },\n    { id: 'scheduled', label: 'المجدولة', count: uploadCounts.scheduled },\n    { id: 'recurring', label: 'المتكررة', count: uploadCounts.recurring }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Breadcrumb Navigation */}\n      <nav className=\"pt-4 px-6\" aria-label=\"Breadcrumb\">\n        <ol className=\"flex items-center space-x-2 text-sm text-gray-500\" dir=\"rtl\">\n          <li>\n            <a href=\"/dashboard\" className=\"hover:text-gray-700 transition-colors\">\n              لوحة التحكم\n            </a>\n          </li>\n          <ChevronRightIcon className=\"h-4 w-4\" />\n          <li>\n            <a href=\"/products\" className=\"hover:text-gray-700 transition-colors\">\n              المنتجات\n            </a>\n          </li>\n          <ChevronRightIcon className=\"h-4 w-4\" />\n          <li className=\"text-gray-800 font-medium\" aria-current=\"page\">\n            استيراد المنتجات\n          </li>\n        </ol>\n      </nav>\n\n      {/* Page Header */}\n      <div className=\"px-6 mt-6\" dir=\"rtl\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <h1 className=\"text-3xl font-semibold text-gray-900\">\n              استيراد المنتجات\n            </h1>\n            <span className=\"mr-2 text-3xl text-gray-500\">\n              ({getTotalUploads()})\n            </span>\n          </div>\n\n          {/* User Menu */}\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <div className=\"flex items-center space-x-2 space-x-reverse text-sm text-gray-600\">\n              <UserIcon className=\"h-4 w-4\" />\n              <span>{user?.firstName} {user?.lastName}</span>\n              {user?.accountType === 'trial' && (\n                <span className=\"bg-amber-100 text-amber-800 px-2 py-1 rounded-full text-xs font-medium\">\n                  تجريبي\n                </span>\n              )}\n            </div>\n            <button\n              onClick={handleLogout}\n              className=\"flex items-center space-x-1 space-x-reverse text-sm text-gray-500 hover:text-gray-700 transition-colors\"\n              title=\"تسجيل الخروج\"\n            >\n              <ArrowRightOnRectangleIcon className=\"h-4 w-4\" />\n              <span>تسجيل الخروج</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"px-6 mt-8\" dir=\"rtl\">\n        <div className=\"border-b border-gray-200 bg-white\">\n          <div className=\"flex items-center justify-between px-6 py-0\">\n            <nav className=\"flex space-x-8 space-x-reverse\" aria-label=\"Tabs\">\n              {tabs.map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => handleTabChange(tab.id)}\n                  className={`\n                    py-3 px-4 text-sm font-medium border-b-2 transition-all duration-200\n                    ${activeTab === tab.id\n                      ? 'border-amber-500 text-gray-800 bg-amber-50'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                    }\n                  `}\n                  aria-current={activeTab === tab.id ? 'page' : undefined}\n                >\n                  {tab.label} ({tab.count})\n                </button>\n              ))}\n            </nav>\n\n            {/* Add Filter Button */}\n            <button\n              onClick={handleFilterClick}\n              className=\"flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors\"\n            >\n              <FunnelIcon className=\"h-4 w-4 ml-2 text-gray-500\" />\n              إضافة فلتر\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Tab Content */}\n      <div className=\"px-6 mt-0\">\n        {activeTab === 'drafts' && (\n          <VariantsTab \n            onCountUpdate={(newCount) => \n              setUploadCounts(prev => ({ ...prev, drafts: newCount }))\n            }\n          />\n        )}\n        {activeTab === 'scheduled' && (\n          <div className=\"py-12 text-center\" dir=\"rtl\">\n            <div className=\"text-gray-500\">\n              <h3 className=\"text-lg font-medium\">لا توجد منتجات مجدولة</h3>\n              <p className=\"mt-2\">ستظهر المنتجات المجدولة هنا عند إنشائها.</p>\n            </div>\n          </div>\n        )}\n        {activeTab === 'recurring' && (\n          <div className=\"py-12 text-center\" dir=\"rtl\">\n            <div className=\"text-gray-500\">\n              <h3 className=\"text-lg font-medium\">لا توجد منتجات متكررة</h3>\n              <p className=\"mt-2\">ستظهر جداول الرفع المتكررة هنا عند تكوينها.</p>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Loading Overlay */}\n      {isLoading && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-25 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 flex items-center space-x-3\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500\"></div>\n            <span className=\"text-gray-700\">Loading...</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default UploadScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,gBAAgB,EAAEC,UAAU,EAAEC,yBAAyB,EAAEC,QAAQ,QAAQ,6BAA6B;AAC/G,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,QAAQ,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,QAAQ,CAAC;EACpD,MAAM,CAACc,YAAY,EAAEC,eAAe,CAAC,GAAGf,QAAQ,CAAC;IAC/CgB,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAEqB;EAAU,CAAC,GAAGd,QAAQ,CAAC,CAAC;;EAEhC;EACA,MAAMe,IAAI,GAAG;IACXC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,MAAM;IAChBC,WAAW,EAAE;EACf,CAAC;;EAED;EACAxB,SAAS,CAAC,MAAM;IACdyB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFN,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,wBAAwB,CAAC;MACtD,IAAID,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClChB,eAAe,CAACe,IAAI,CAACE,MAAM,IAAIlB,YAAY,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDZ,SAAS,CAAC,8BAA8B,EAAE,OAAO,CAAC;IACpD,CAAC,SAAS;MACRD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMe,eAAe,GAAGA,CAAA,KAAM;IAC5B,OAAOrB,YAAY,CAACE,MAAM,GAAGF,YAAY,CAACG,SAAS,GAAGH,YAAY,CAACI,SAAS;EAC9E,CAAC;EAED,MAAMkB,eAAe,GAAIC,OAAO,IAAK;IACnCxB,YAAY,CAACwB,OAAO,CAAC;EACvB,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACAjB,SAAS,CAAC,kCAAkC,EAAE,MAAM,CAAC;EACvD,CAAC;EAED,MAAMkB,YAAY,GAAGA,CAAA,KAAM;IACzBlB,SAAS,CAAC,uBAAuB,EAAE,SAAS,CAAC;IAC7C;EACF,CAAC;EAED,MAAMmB,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,QAAQ;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE7B,YAAY,CAACE;EAAO,CAAC,EAC/D;IAAEyB,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE7B,YAAY,CAACG;EAAU,CAAC,EACrE;IAAEwB,EAAE,EAAE,WAAW;IAAEC,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE7B,YAAY,CAACI;EAAU,CAAC,CACtE;EAED,oBACET,OAAA;IAAKmC,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCpC,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAAC,cAAW,YAAY;MAAAC,QAAA,eAChDpC,OAAA;QAAImC,SAAS,EAAC,mDAAmD;QAACE,GAAG,EAAC,KAAK;QAAAD,QAAA,gBACzEpC,OAAA;UAAAoC,QAAA,eACEpC,OAAA;YAAGsC,IAAI,EAAC,YAAY;YAACH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEvE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACL1C,OAAA,CAACP,gBAAgB;UAAC0C,SAAS,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxC1C,OAAA;UAAAoC,QAAA,eACEpC,OAAA;YAAGsC,IAAI,EAAC,WAAW;YAACH,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtE;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACL1C,OAAA,CAACP,gBAAgB;UAAC0C,SAAS,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxC1C,OAAA;UAAImC,SAAS,EAAC,2BAA2B;UAAC,gBAAa,MAAM;UAAAC,QAAA,EAAC;QAE9D;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGN1C,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAACE,GAAG,EAAC,KAAK;MAAAD,QAAA,eAClCpC,OAAA;QAAKmC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDpC,OAAA;UAAKmC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCpC,OAAA;YAAImC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAErD;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL1C,OAAA;YAAMmC,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GAAC,GAC3C,EAACV,eAAe,CAAC,CAAC,EAAC,GACtB;UAAA;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAGN1C,OAAA;UAAKmC,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1DpC,OAAA;YAAKmC,SAAS,EAAC,mEAAmE;YAAAC,QAAA,gBAChFpC,OAAA,CAACJ,QAAQ;cAACuC,SAAS,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChC1C,OAAA;cAAAoC,QAAA,GAAOvB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,SAAS,EAAC,GAAC,EAACD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,QAAQ;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC9C,CAAA7B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,WAAW,MAAK,OAAO,iBAC5BhB,OAAA;cAAMmC,SAAS,EAAC,wEAAwE;cAAAC,QAAA,EAAC;YAEzF;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN1C,OAAA;YACE2C,OAAO,EAAEb,YAAa;YACtBK,SAAS,EAAC,yGAAyG;YACnHS,KAAK,EAAC,qEAAc;YAAAR,QAAA,gBAEpBpC,OAAA,CAACL,yBAAyB;cAACwC,SAAS,EAAC;YAAS;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjD1C,OAAA;cAAAoC,QAAA,EAAM;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAACE,GAAG,EAAC,KAAK;MAAAD,QAAA,eAClCpC,OAAA;QAAKmC,SAAS,EAAC,mCAAmC;QAAAC,QAAA,eAChDpC,OAAA;UAAKmC,SAAS,EAAC,6CAA6C;UAAAC,QAAA,gBAC1DpC,OAAA;YAAKmC,SAAS,EAAC,gCAAgC;YAAC,cAAW,MAAM;YAAAC,QAAA,EAC9DL,IAAI,CAACc,GAAG,CAAEC,GAAG,iBACZ9C,OAAA;cAEE2C,OAAO,EAAEA,CAAA,KAAMhB,eAAe,CAACmB,GAAG,CAACd,EAAE,CAAE;cACvCG,SAAS,EAAE;AAC7B;AACA,sBAAsBhC,SAAS,KAAK2C,GAAG,CAACd,EAAE,GAClB,4CAA4C,GAC5C,uEAAuE;AAC/F,mBACoB;cACF,gBAAc7B,SAAS,KAAK2C,GAAG,CAACd,EAAE,GAAG,MAAM,GAAGe,SAAU;cAAAX,QAAA,GAEvDU,GAAG,CAACb,KAAK,EAAC,IAAE,EAACa,GAAG,CAACZ,KAAK,EAAC,GAC1B;YAAA,GAZOY,GAAG,CAACd,EAAE;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYL,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN1C,OAAA;YACE2C,OAAO,EAAEd,iBAAkB;YAC3BM,SAAS,EAAC,2OAA2O;YAAAC,QAAA,gBAErPpC,OAAA,CAACN,UAAU;cAACyC,SAAS,EAAC;YAA4B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,2DAEvD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1C,OAAA;MAAKmC,SAAS,EAAC,WAAW;MAAAC,QAAA,GACvBjC,SAAS,KAAK,QAAQ,iBACrBH,OAAA,CAACH,WAAW;QACVmD,aAAa,EAAGC,QAAQ,IACtB3C,eAAe,CAAC4C,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE3C,MAAM,EAAE0C;QAAS,CAAC,CAAC;MACxD;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACF,EACAvC,SAAS,KAAK,WAAW,iBACxBH,OAAA;QAAKmC,SAAS,EAAC,mBAAmB;QAACE,GAAG,EAAC,KAAK;QAAAD,QAAA,eAC1CpC,OAAA;UAAKmC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpC,OAAA;YAAImC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D1C,OAAA;YAAGmC,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAAwC;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EACAvC,SAAS,KAAK,WAAW,iBACxBH,OAAA;QAAKmC,SAAS,EAAC,mBAAmB;QAACE,GAAG,EAAC,KAAK;QAAAD,QAAA,eAC1CpC,OAAA;UAAKmC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BpC,OAAA;YAAImC,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAqB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9D1C,OAAA;YAAGmC,SAAS,EAAC,MAAM;YAAAC,QAAA,EAAC;UAA2C;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLhC,SAAS,iBACRV,OAAA;MAAKmC,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFpC,OAAA;QAAKmC,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClEpC,OAAA;UAAKmC,SAAS,EAAC;QAA8D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpF1C,OAAA;UAAMmC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAU;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxC,EAAA,CAjMID,YAAY;EAAA,QAQMH,QAAQ;AAAA;AAAAqD,EAAA,GAR1BlD,YAAY;AAmMlB,eAAeA,YAAY;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}